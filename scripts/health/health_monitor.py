#!/usr/bin/env python3
"""
健康检查监控服务

监控所有服务的健康状态，提供统一的健康检查仪表板
"""

import asyncio
import aiohttp
import time
from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum


class ServiceStatus(str, Enum):
    """服务状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"
    UNHEALTHY = "unhealthy"
    ERROR = "error"
    UNREACHABLE = "unreachable"


@dataclass
class ServiceInfo:
    """服务信息"""
    name: str
    port: int
    url: str
    status: ServiceStatus = ServiceStatus.UNREACHABLE
    response_time_ms: float = 0.0
    last_check: float = 0.0
    error_message: str = ""
    health_data: Dict[str, Any] = None


class HealthMonitor:
    """健康检查监控器"""
    
    def __init__(self):
        self.services = {
            "manticore_search": ServiceInfo("manticore_search", 9000, "http://localhost:9000"),
            "embedding_service": ServiceInfo("embedding_service", 9001, "http://localhost:9001"),
            "user_service": ServiceInfo("user_service", 9002, "http://localhost:9002"),
            "api_gateway": ServiceInfo("api_gateway", 9003, "http://localhost:9003"),
            "topic_service": ServiceInfo("topic_service", 9004, "http://localhost:9004"),
            "document_service": ServiceInfo("document_service", 9005, "http://localhost:9005"),
            "llm_integration": ServiceInfo("llm_integration", 9006, "http://localhost:9006"),
            "conversation_service": ServiceInfo("conversation_service", 9007, "http://localhost:9007"),
            "summary_service": ServiceInfo("summary_service", 9008, "http://localhost:9008"),
        }
    
    async def check_service_health(self, service: ServiceInfo, timeout: int = 5) -> ServiceInfo:
        """检查单个服务的健康状态"""
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                async with session.get(f"{service.url}/api/v1/health") as response:
                    response_time = (time.time() - start_time) * 1000
                    service.response_time_ms = response_time
                    service.last_check = time.time()
                    
                    if response.status == 200:
                        health_data = await response.json()
                        service.health_data = health_data
                        
                        # 根据健康检查响应确定状态
                        if health_data.get("status") == "healthy":
                            service.status = ServiceStatus.HEALTHY
                        elif health_data.get("status") == "warning":
                            service.status = ServiceStatus.WARNING
                        elif health_data.get("status") == "unhealthy":
                            service.status = ServiceStatus.UNHEALTHY
                        else:
                            service.status = ServiceStatus.ERROR
                        
                        service.error_message = health_data.get("message", "")
                    else:
                        service.status = ServiceStatus.ERROR
                        service.error_message = f"HTTP {response.status}"
                        service.health_data = None
                        
        except asyncio.TimeoutError:
            service.response_time_ms = timeout * 1000
            service.status = ServiceStatus.UNREACHABLE
            service.error_message = "Request timeout"
            service.last_check = time.time()
            service.health_data = None
        except Exception as e:
            service.response_time_ms = (time.time() - start_time) * 1000
            service.status = ServiceStatus.UNREACHABLE
            service.error_message = str(e)
            service.last_check = time.time()
            service.health_data = None
        
        return service
    
    async def check_all_services(self) -> Dict[str, ServiceInfo]:
        """检查所有服务的健康状态"""
        tasks = [self.check_service_health(service) for service in self.services.values()]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 更新服务状态
        for i, (service_name, service) in enumerate(self.services.items()):
            if not isinstance(results[i], Exception):
                self.services[service_name] = results[i]
        
        return self.services
    
    def get_overall_status(self) -> ServiceStatus:
        """获取整体健康状态"""
        statuses = [service.status for service in self.services.values()]
        
        if ServiceStatus.ERROR in statuses:
            return ServiceStatus.ERROR
        elif ServiceStatus.UNHEALTHY in statuses:
            return ServiceStatus.UNHEALTHY
        elif ServiceStatus.UNREACHABLE in statuses:
            return ServiceStatus.UNREACHABLE
        elif ServiceStatus.WARNING in statuses:
            return ServiceStatus.WARNING
        else:
            return ServiceStatus.HEALTHY
    
    def print_status_report(self):
        """打印状态报告"""
        print("\n" + "="*80)
        print("🏥 Master-Know 系统健康状态报告")
        print("="*80)
        
        overall_status = self.get_overall_status()
        status_emoji = {
            ServiceStatus.HEALTHY: "✅",
            ServiceStatus.WARNING: "⚠️",
            ServiceStatus.UNHEALTHY: "❌",
            ServiceStatus.ERROR: "💥",
            ServiceStatus.UNREACHABLE: "🔌"
        }
        
        print(f"整体状态: {status_emoji[overall_status]} {overall_status.value.upper()}")
        print(f"检查时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 按状态分组显示
        status_groups = {}
        for service in self.services.values():
            if service.status not in status_groups:
                status_groups[service.status] = []
            status_groups[service.status].append(service)
        
        for status in [ServiceStatus.HEALTHY, ServiceStatus.WARNING, ServiceStatus.UNHEALTHY, ServiceStatus.ERROR, ServiceStatus.UNREACHABLE]:
            if status in status_groups:
                services = status_groups[status]
                print(f"{status_emoji[status]} {status.value.upper()} ({len(services)} 个服务):")
                
                for service in services:
                    print(f"  📍 {service.name:20} - 端口:{service.port:4} - 响应时间:{service.response_time_ms:6.1f}ms")
                    if service.error_message:
                        print(f"     💬 {service.error_message}")
                print()
        
        # 统计信息
        total_services = len(self.services)
        healthy_count = len([s for s in self.services.values() if s.status == ServiceStatus.HEALTHY])
        warning_count = len([s for s in self.services.values() if s.status == ServiceStatus.WARNING])
        unhealthy_count = len([s for s in self.services.values() if s.status in [ServiceStatus.UNHEALTHY, ServiceStatus.ERROR, ServiceStatus.UNREACHABLE]])
        
        print("📊 统计信息:")
        print(f"  总服务数: {total_services}")
        print(f"  健康服务: {healthy_count}")
        print(f"  警告服务: {warning_count}")
        print(f"  异常服务: {unhealthy_count}")
        print(f"  健康率: {healthy_count/total_services*100:.1f}%")
        print("="*80)


async def main():
    """主函数"""
    monitor = HealthMonitor()
    
    print("🔍 开始检查所有服务健康状态...")
    
    # 检查所有服务
    await monitor.check_all_services()
    
    # 打印报告
    monitor.print_status_report()
    
    # 返回整体状态
    overall_status = monitor.get_overall_status()
    return overall_status == ServiceStatus.HEALTHY


if __name__ == "__main__":
    success = asyncio.run(main())
