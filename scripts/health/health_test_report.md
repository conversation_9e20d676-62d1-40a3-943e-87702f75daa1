# 健康检查服务测试报告

## 📊 测试概览

- **总服务数**: 7
- **测试成功**: 7
- **测试失败**: 0
- **成功率**: 100.0%

## 📋 详细结果

| 服务名称 | 状态 | 整体健康 | 就绪状态 | 运行时间 | CPU | 内存 | 错误信息 |
|---------|------|----------|----------|----------|-----|------|----------|
| user_service | ✅ 成功 | warning | ready | 0.3s | 15.6% | 53.5% | - |
| topic_service | ✅ 成功 | error | not_ready | 0.7s | 13.1% | 53.5% | - |
| api_gateway | ✅ 成功 | error | not_ready | 1.1s | 6.8% | 53.5% | - |
| document_service | ✅ 成功 | error | not_ready | 0.3s | 10.0% | 53.5% | - |
| llm_integration | ✅ 成功 | error | not_ready | 0.1s | 11.0% | 53.5% | - |
| conversation_service | ✅ 成功 | error | not_ready | 0.9s | 10.7% | 53.5% | - |
| summary_service | ✅ 成功 | error | not_ready | 0.8s | 6.8% | 53.5% | - |

## ✅ 成功的服务详情

### user_service
- **整体状态**: warning
- **运行时间**: 0.25秒
- **依赖服务数**: 2
- **系统CPU**: 15.6%
- **系统内存**: 53.5%
- **消息**: Some systems showing warnings

### topic_service
- **整体状态**: error
- **运行时间**: 0.68秒
- **依赖服务数**: 5
- **系统CPU**: 13.1%
- **系统内存**: 53.5%
- **消息**: Error in services: user_service, document_service, embedding_service

### api_gateway
- **整体状态**: error
- **运行时间**: 1.12秒
- **依赖服务数**: 9
- **系统CPU**: 6.8%
- **系统内存**: 53.5%
- **消息**: Error in services: user_service, topic_service, document_service, embedding_service, llm_integration, conversation_service, summary_service, manticore_search

### document_service
- **整体状态**: error
- **运行时间**: 0.35秒
- **依赖服务数**: 4
- **系统CPU**: 10.0%
- **系统内存**: 53.5%
- **消息**: Error in services: embedding_service, manticore_search

### llm_integration
- **整体状态**: error
- **运行时间**: 0.13秒
- **依赖服务数**: 3
- **系统CPU**: 11.0%
- **系统内存**: 53.5%
- **消息**: Error in services: manticore_search, embedding_service

### conversation_service
- **整体状态**: error
- **运行时间**: 0.88秒
- **依赖服务数**: 6
- **系统CPU**: 10.7%
- **系统内存**: 53.5%
- **消息**: Error in services: user_service, topic_service, llm_integration, summary_service

### summary_service
- **整体状态**: error
- **运行时间**: 0.76秒
- **依赖服务数**: 5
- **系统CPU**: 6.8%
- **系统内存**: 53.5%
- **消息**: Error in services: llm_integration, conversation_service, document_service

## 🔧 建议

🎉 所有服务的健康检查都正常工作！系统已准备好进入下一阶段。