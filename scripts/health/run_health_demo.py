#!/usr/bin/env python3
"""
健康检查系统演示脚本

启动一个服务并演示健康检查功能
"""

import asyncio
import subprocess
import time
import signal
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


class HealthCheckDemo:
    """健康检查演示器"""
    
    def __init__(self):
        self.processes = []
        self.project_root = project_root
    
    def start_service(self, service_name: str, port: int) -> subprocess.Popen:
        """启动一个服务"""
        service_path = self.project_root / service_name
        main_file = service_path / "main.py"
        
        if not main_file.exists():
            print(f"⚠️ {service_name}/main.py 不存在，跳过启动")
            return None
        
        print(f"🚀 启动 {service_name} (端口 {port})...")
        
        # 启动服务进程
        cmd = [
            sys.executable, str(main_file)
        ]
        
        env = os.environ.copy()
        env['PYTHONPATH'] = str(self.project_root)
        
        process = subprocess.Popen(
            cmd,
            cwd=str(self.project_root),
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        self.processes.append(process)
        return process
    
    async def wait_for_service(self, port: int, timeout: int = 10) -> bool:
        """等待服务启动"""
        import aiohttp
        
        for i in range(timeout):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"http://localhost:{port}/api/v1/health", timeout=1) as response:
                        if response.status == 200:
                            return True
            except:
                pass
            
            await asyncio.sleep(1)
        
        return False
    
    async def test_health_endpoints(self, port: int) -> dict:
        """测试健康检查端点"""
        import aiohttp
        
        results = {}
        
        try:
            async with aiohttp.ClientSession() as session:
                # 测试健康检查端点
                async with session.get(f"http://localhost:{port}/api/v1/health") as response:
                    if response.status == 200:
                        health_data = await response.json()
                        results['health'] = {
                            'status': 'success',
                            'data': health_data,
                            'response_time': response.headers.get('X-Response-Time', 'N/A')
                        }
                    else:
                        results['health'] = {
                            'status': 'error',
                            'error': f"HTTP {response.status}"
                        }
                
                # 测试就绪检查端点
                async with session.get(f"http://localhost:{port}/api/v1/ready") as response:
                    if response.status == 200:
                        ready_data = await response.json()
                        results['ready'] = {
                            'status': 'success',
                            'data': ready_data
                        }
                    else:
                        results['ready'] = {
                            'status': 'error',
                            'error': f"HTTP {response.status}"
                        }
        
        except Exception as e:
            results['error'] = str(e)
        
        return results
    
    def cleanup(self):
        """清理进程"""
        print("\n🧹 清理进程...")
        for process in self.processes:
            if process.poll() is None:  # 进程还在运行
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
        
        self.processes.clear()
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在清理...")
        self.cleanup()
        sys.exit(0)


async def main():
    """主函数"""
    demo = HealthCheckDemo()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, demo.signal_handler)
    signal.signal(signal.SIGTERM, demo.signal_handler)
    
    try:
        print("🎭 健康检查系统演示")
        print("=" * 50)
        
        # 启动用户服务
        user_service_port = 9002
        process = demo.start_service("user_service", user_service_port)
        
        if process is None:
            print("❌ 无法启动用户服务")
            return False
        
        print("⏳ 等待服务启动...")
        service_ready = await demo.wait_for_service(user_service_port, timeout=10)
        
        if not service_ready:
            print("❌ 服务启动超时")
            return False
        
        print("✅ 服务启动成功！")
        print()
        
        # 测试健康检查端点
        print("🧪 测试健康检查端点...")
        test_results = await demo.test_health_endpoints(user_service_port)
        
        if 'error' in test_results:
            print(f"❌ 测试失败: {test_results['error']}")
            return False
        
        # 显示健康检查结果
        if test_results.get('health', {}).get('status') == 'success':
            health_data = test_results['health']['data']
            print("✅ 健康检查端点测试通过")
            print(f"   状态: {health_data.get('status', 'unknown')}")
            print(f"   服务: {health_data.get('service_name', 'unknown')}")
            print(f"   版本: {health_data.get('version', 'unknown')}")
            print(f"   运行时间: {health_data.get('uptime_seconds', 0):.2f}秒")
            print(f"   依赖服务: {len(health_data.get('services', {}))}")
        else:
            print("❌ 健康检查端点测试失败")
            return False
        
        # 显示就绪检查结果
        if test_results.get('ready', {}).get('status') == 'success':
            ready_data = test_results['ready']['data']
            print("✅ 就绪检查端点测试通过")
            print(f"   就绪状态: {ready_data.get('status', 'unknown')}")
        else:
            print("❌ 就绪检查端点测试失败")
            return False
        
        print()
        print("🎉 健康检查系统演示成功！")
        print()
        print("📋 演示内容:")
        print("  ✅ 服务启动和健康检查")
        print("  ✅ 统一健康检查框架")
        print("  ✅ 标准化健康检查端点")
        print("  ✅ 就绪探针支持")
        print("  ✅ 依赖服务检查")
        print("  ✅ 系统资源监控")
        
        print()
        print("🔗 可用端点:")
        print(f"  健康检查: http://localhost:{user_service_port}/api/v1/health")
        print(f"  就绪检查: http://localhost:{user_service_port}/api/v1/ready")
        print(f"  服务根路径: http://localhost:{user_service_port}/")
        
        # 保持服务运行一段时间
        print()
        print("⏰ 服务将运行10秒钟，然后自动停止...")
        await asyncio.sleep(10)
        
        return True
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        return False
    
    finally:
        demo.cleanup()


if __name__ == "__main__":
    success = asyncio.run(main())
    
    if success:
        print("\n🎯 第二阶段：健康检查统一 - 演示成功完成！")
        sys.exit(0)
    else:
        print("\n⚠️ 演示过程中遇到问题")
        sys.exit(1)
