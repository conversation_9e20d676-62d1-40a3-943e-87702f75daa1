#!/usr/bin/env python3
"""
健康检查实施验证脚本

验证第二阶段健康检查统一的完成情况
"""

import os
import sys
import asyncio
from pathlib import Path
from typing import Dict, List, Tuple

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


def check_health_service_files() -> List[Tuple[str, bool, str]]:
    """检查健康检查服务文件是否存在"""
    results = []
    
    services = [
        "user_service",
        "topic_service", 
        "api_gateway",
        "document_service",
        "llm_integration",
        "conversation_service",
        "summary_service"
    ]
    
    for service in services:
        health_file = project_root / service / "services" / "health_service.py"
        
        if health_file.exists():
            # 检查文件内容是否包含必要的组件
            with open(health_file, "r", encoding="utf-8") as f:
                content = f.read()
            
            required_components = [
                "HealthChecker",
                "get_health_status",
                "get_ready_status",
                "check_dependencies",
                "check_self"
            ]
            
            missing_components = [comp for comp in required_components if comp not in content]
            
            if not missing_components:
                results.append((service, True, "健康检查服务完整"))
            else:
                results.append((service, False, f"缺少组件: {', '.join(missing_components)}"))
        else:
            results.append((service, False, "健康检查服务文件不存在"))
    
    return results


def check_health_framework() -> Tuple[bool, str]:
    """检查健康检查框架是否存在"""
    framework_file = project_root / "scripts" / "health" / "health_framework.py"
    
    if not framework_file.exists():
        return False, "健康检查框架文件不存在"
    
    with open(framework_file, "r", encoding="utf-8") as f:
        content = f.read()
    
    required_classes = [
        "HealthStatus",
        "ServiceHealth", 
        "SystemHealth",
        "OverallHealth",
        "HealthChecker",
        "DatabaseHealthChecker"
    ]
    
    missing_classes = [cls for cls in required_classes if cls not in content]
    
    if not missing_classes:
        return True, "健康检查框架完整"
    else:
        return False, f"缺少类: {', '.join(missing_classes)}"


def check_monitoring_tools() -> List[Tuple[str, bool, str]]:
    """检查监控工具是否存在"""
    results = []
    
    tools = [
        ("health_monitor.py", "健康状态监控器"),
        ("test_health_services.py", "健康检查测试脚本"),
        ("generate_health_services.py", "健康检查生成脚本")
    ]
    
    for tool_file, description in tools:
        tool_path = project_root / "scripts" / "health" / tool_file
        
        if tool_path.exists():
            results.append((description, True, "工具存在且可用"))
        else:
            results.append((description, False, "工具文件不存在"))
    
    return results


async def test_health_framework_functionality():
    """测试健康检查框架功能"""
    try:
        from scripts.health.health_framework import HealthChecker, ServiceHealth, HealthStatus
        
        # 创建一个测试健康检查器
        class TestHealthChecker(HealthChecker):
            def __init__(self):
                super().__init__("test_service", "1.0.0")
            
            async def check_dependencies(self):
                return {}
            
            async def check_self(self):
                return ServiceHealth(
                    name="test_service",
                    status=HealthStatus.HEALTHY,
                    response_time_ms=0.0,
                    message="Test service is healthy",
                    timestamp=0.0
                )
        
        # 测试健康检查器
        checker = TestHealthChecker()
        health = await checker.get_health_status()
        
        if health.status == HealthStatus.HEALTHY:
            return True, "健康检查框架功能正常"
        else:
            return False, f"健康检查框架返回异常状态: {health.status}"
            
    except Exception as e:
        return False, f"健康检查框架测试失败: {str(e)}"


def generate_validation_report(
    health_files: List[Tuple[str, bool, str]],
    framework_status: Tuple[bool, str],
    monitoring_tools: List[Tuple[str, bool, str]],
    framework_test: Tuple[bool, str]
) -> str:
    """生成验证报告"""
    
    report = []
    report.append("# 健康检查统一实施验证报告")
    report.append("")
    report.append("## 📊 验证概览")
    report.append("")
    
    # 统计信息
    health_files_success = sum(1 for _, success, _ in health_files if success)
    health_files_total = len(health_files)
    
    monitoring_tools_success = sum(1 for _, success, _ in monitoring_tools if success)
    monitoring_tools_total = len(monitoring_tools)
    
    framework_success = 1 if framework_status[0] else 0
    framework_test_success = 1 if framework_test[0] else 0
    
    total_checks = health_files_total + monitoring_tools_total + 2  # +2 for framework and test
    total_success = health_files_success + monitoring_tools_success + framework_success + framework_test_success
    
    report.append(f"- **总检查项**: {total_checks}")
    report.append(f"- **通过项**: {total_success}")
    report.append(f"- **失败项**: {total_checks - total_success}")
    report.append(f"- **完成率**: {total_success/total_checks*100:.1f}%")
    report.append("")
    
    # 健康检查服务文件
    report.append("## 🏥 健康检查服务文件")
    report.append("")
    report.append("| 服务名称 | 状态 | 说明 |")
    report.append("|---------|------|------|")
    
    for service, success, message in health_files:
        status_icon = "✅" if success else "❌"
        report.append(f"| {service} | {status_icon} | {message} |")
    
    report.append("")
    report.append(f"健康检查服务完成率: {health_files_success}/{health_files_total} ({health_files_success/health_files_total*100:.1f}%)")
    report.append("")
    
    # 健康检查框架
    report.append("## 🔧 健康检查框架")
    report.append("")
    framework_icon = "✅" if framework_status[0] else "❌"
    report.append(f"- **框架状态**: {framework_icon} {framework_status[1]}")
    
    test_icon = "✅" if framework_test[0] else "❌"
    report.append(f"- **功能测试**: {test_icon} {framework_test[1]}")
    report.append("")
    
    # 监控工具
    report.append("## 🔍 监控工具")
    report.append("")
    report.append("| 工具名称 | 状态 | 说明 |")
    report.append("|---------|------|------|")
    
    for tool, success, message in monitoring_tools:
        status_icon = "✅" if success else "❌"
        report.append(f"| {tool} | {status_icon} | {message} |")
    
    report.append("")
    report.append(f"监控工具完成率: {monitoring_tools_success}/{monitoring_tools_total} ({monitoring_tools_success/monitoring_tools_total*100:.1f}%)")
    report.append("")
    
    # 总结
    report.append("## 🎯 总结")
    report.append("")
    
    if total_success == total_checks:
        report.append("🎉 **第二阶段：健康检查统一 - 完美完成！**")
        report.append("")
        report.append("所有健康检查组件都已正确实施：")
        report.append("- ✅ 统一健康检查框架")
        report.append("- ✅ 所有服务健康检查实现")
        report.append("- ✅ 监控和测试工具")
        report.append("- ✅ 标准化健康检查端点")
        report.append("")
        report.append("系统现在具备了：")
        report.append("- 🏥 统一的健康检查标准")
        report.append("- 📊 实时健康状态监控")
        report.append("- 🔍 自动化健康检查测试")
        report.append("- 🚀 Kubernetes就绪探针支持")
    else:
        report.append("⚠️ **第二阶段实施未完成**")
        report.append("")
        report.append("需要解决的问题：")
        
        # 列出失败的项目
        if not framework_status[0]:
            report.append(f"- ❌ 健康检查框架: {framework_status[1]}")
        
        if not framework_test[0]:
            report.append(f"- ❌ 框架功能测试: {framework_test[1]}")
        
        for service, success, message in health_files:
            if not success:
                report.append(f"- ❌ {service}: {message}")
        
        for tool, success, message in monitoring_tools:
            if not success:
                report.append(f"- ❌ {tool}: {message}")
    
    report.append("")
    report.append("---")
    report.append(f"**报告生成时间**: {__import__('time').strftime('%Y-%m-%d %H:%M:%S')}")
    
    return "\n".join(report)


async def main():
    """主函数"""
    print("🔍 开始验证健康检查统一实施...")
    print("=" * 60)
    
    # 检查健康检查服务文件
    print("📁 检查健康检查服务文件...")
    health_files = check_health_service_files()
    
    # 检查健康检查框架
    print("🔧 检查健康检查框架...")
    framework_status = check_health_framework()
    
    # 检查监控工具
    print("🔍 检查监控工具...")
    monitoring_tools = check_monitoring_tools()
    
    # 测试框架功能
    print("🧪 测试健康检查框架功能...")
    framework_test = await test_health_framework_functionality()
    
    # 生成报告
    report = generate_validation_report(
        health_files, framework_status, monitoring_tools, framework_test
    )
    
    # 保存报告
    report_file = project_root / "scripts" / "health" / "health_validation_report.md"
    with open(report_file, "w", encoding="utf-8") as f:
        f.write(report)
    
    # 显示结果
    print("\n" + "=" * 60)
    print("验证结果:")
    
    success_count = 0
    total_count = 0
    
    for service, success, message in health_files:
        status = "✅" if success else "❌"
        print(f"  {status} {service}: {message}")
        if success:
            success_count += 1
        total_count += 1
    
    framework_status_icon = "✅" if framework_status[0] else "❌"
    print(f"  {framework_status_icon} 健康检查框架: {framework_status[1]}")
    if framework_status[0]:
        success_count += 1
    total_count += 1
    
    framework_test_icon = "✅" if framework_test[0] else "❌"
    print(f"  {framework_test_icon} 框架功能测试: {framework_test[1]}")
    if framework_test[0]:
        success_count += 1
    total_count += 1
    
    for tool, success, message in monitoring_tools:
        status = "✅" if success else "❌"
        print(f"  {status} {tool}: {message}")
        if success:
            success_count += 1
        total_count += 1
    
    print(f"\n📊 完成率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    print(f"📄 详细报告已保存到: {report_file}")
    
    if success_count == total_count:
        print("\n🎉 第二阶段：健康检查统一 - 完美完成！")
        return True
    else:
        print(f"\n⚠️ 还有 {total_count - success_count} 个项目需要完成")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
