#!/usr/bin/env python3
"""
服务边界明确测试脚本

测试API版本化、服务间通信和契约测试功能：
- API框架功能测试
- 服务客户端测试
- 契约测试验证
- 服务治理功能测试
"""

import asyncio
import json
import logging
import tempfile
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))

from fastapi import FastAPI, HTTPException
from fastapi.testclient import TestClient
import uvicorn

from shared.api_framework import (
    setup_api_framework, APIVersion, BaseAPIRequest, DataResponse,
    create_success_response, APIExceptionHandler, ErrorCode
)
from shared.service_client import ServiceClient, ServiceEndpoint, get_service_client_manager
from shared.contract_testing import ContractTester, generate_test_cases_from_openapi
from shared.service_governance import ServiceRegistry, ServiceInstance, ServiceStatus

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


# 测试API模型
class CreateUserRequest(BaseAPIRequest):
    username: str
    email: str
    full_name: str = None


class UserResponse(BaseAPIRequest):
    id: str
    username: str
    email: str
    full_name: str = None
    created_at: str


class ServiceBoundaryTester:
    """服务边界测试器"""
    
    def __init__(self):
        self.test_app = None
        self.test_client = None
        self.test_server_task = None
        self.service_registry = None
    
    def create_test_app(self) -> FastAPI:
        """创建测试应用"""
        app = FastAPI()
        
        # 设置API框架
        setup_api_framework(
            app=app,
            service_name="test_service",
            service_version="1.0.0",
            service_description="Test service for boundary testing",
            default_api_version=APIVersion.V1
        )
        
        # 添加测试端点
        @app.get("/api/v1/users/{user_id}", response_model=DataResponse)
        async def get_user(user_id: str):
            """获取用户信息"""
            if user_id == "404":
                raise HTTPException(status_code=404, detail="User not found")
            
            user_data = {
                "id": user_id,
                "username": f"user_{user_id}",
                "email": f"user_{user_id}@example.com",
                "full_name": f"User {user_id}",
                "created_at": "2024-01-01T00:00:00Z"
            }
            
            return create_success_response(
                data=user_data,
                message="User retrieved successfully"
            )
        
        @app.post("/api/v1/users", response_model=DataResponse)
        async def create_user(request: CreateUserRequest):
            """创建用户"""
            user_data = {
                "id": "new_user_123",
                "username": request.username,
                "email": request.email,
                "full_name": request.full_name,
                "created_at": "2024-01-01T00:00:00Z"
            }
            
            return create_success_response(
                data=user_data,
                message="User created successfully"
            )
        
        @app.get("/api/v1/users", response_model=DataResponse)
        async def list_users(page: int = 1, page_size: int = 20):
            """列出用户"""
            users = [
                {
                    "id": f"user_{i}",
                    "username": f"user_{i}",
                    "email": f"user_{i}@example.com",
                    "full_name": f"User {i}",
                    "created_at": "2024-01-01T00:00:00Z"
                }
                for i in range(1, 6)
            ]
            
            return create_success_response(
                data=users,
                message="Users retrieved successfully"
            )
        
        @app.get("/api/v1/health")
        async def health_check():
            """健康检查"""
            return {"status": "healthy", "timestamp": "2024-01-01T00:00:00Z"}
        
        return app
    
    async def setup(self):
        """设置测试环境"""
        logger.info("Setting up service boundary test environment...")
        
        # 创建测试应用
        self.test_app = self.create_test_app()
        self.test_client = TestClient(self.test_app)
        
        # 启动测试服务器
        config = uvicorn.Config(
            app=self.test_app,
            host="127.0.0.1",
            port=8999,
            log_level="error"
        )
        server = uvicorn.Server(config)
        self.test_server_task = asyncio.create_task(server.serve())
        
        # 等待服务器启动
        await asyncio.sleep(2)
        
        # 设置服务注册表
        self.service_registry = ServiceRegistry()
        await self.service_registry.connect()
        
        logger.info("Test environment setup completed")
    
    async def cleanup(self):
        """清理测试环境"""
        logger.info("Cleaning up test environment...")
        
        if self.test_server_task:
            self.test_server_task.cancel()
            try:
                await self.test_server_task
            except asyncio.CancelledError:
                pass
        
        if self.service_registry:
            await self.service_registry.disconnect()
        
        logger.info("Test environment cleanup completed")
    
    async def test_api_framework(self) -> bool:
        """测试API框架功能"""
        logger.info("Testing API framework...")
        
        try:
            # 测试成功响应
            response = self.test_client.get("/api/v1/users/123")
            assert response.status_code == 200
            
            data = response.json()
            assert data["success"] is True
            assert "request_id" in data
            assert "timestamp" in data
            assert "version" in data
            assert data["data"]["id"] == "123"
            
            # 测试错误响应
            response = self.test_client.get("/api/v1/users/404")
            assert response.status_code == 404
            
            error_data = response.json()
            assert error_data["success"] is False
            assert "error_code" in error_data
            assert "request_id" in error_data
            
            # 测试POST请求
            user_data = {
                "username": "testuser",
                "email": "<EMAIL>",
                "full_name": "Test User"
            }
            response = self.test_client.post("/api/v1/users", json=user_data)
            assert response.status_code == 200
            
            # 检查响应头
            assert "X-Request-ID" in response.headers
            assert "X-API-Version" in response.headers
            assert "X-Service-Name" in response.headers
            
            logger.info("✅ API framework test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ API framework test failed: {e}")
            return False
    
    async def test_service_client(self) -> bool:
        """测试服务客户端"""
        logger.info("Testing service client...")
        
        try:
            # 注册测试服务端点
            endpoint = ServiceEndpoint(
                name="test_service",
                base_url="http://127.0.0.1:8999",
                timeout=10.0
            )
            
            client_manager = get_service_client_manager()
            client_manager.register_endpoint(endpoint)
            
            # 获取客户端
            client = client_manager.get_client("test_client")
            
            # 测试GET请求
            response = await client.get("test_service", "users/123")
            assert response["success"] is True
            assert response["data"]["id"] == "123"
            
            # 测试POST请求
            user_data = {
                "username": "clientuser",
                "email": "<EMAIL>"
            }
            response = await client.post("test_service", "users", data=user_data)
            assert response["success"] is True
            assert response["data"]["username"] == "clientuser"
            
            # 测试健康检查
            health_status = await client.health_check("test_service")
            assert health_status is True
            
            # 测试错误处理
            try:
                await client.get("test_service", "users/404")
                assert False, "Should have raised an exception"
            except Exception:
                pass  # 期望的异常
            
            await client.close()
            
            logger.info("✅ Service client test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Service client test failed: {e}")
            return False
    
    async def test_contract_testing(self) -> bool:
        """测试契约测试功能"""
        logger.info("Testing contract testing...")
        
        try:
            # 获取OpenAPI规范
            response = self.test_client.get("/openapi.json")
            assert response.status_code == 200
            openapi_spec = response.json()
            
            # 创建契约测试器
            tester = ContractTester("http://127.0.0.1:8999", openapi_spec)
            
            # 生成测试用例
            test_cases = generate_test_cases_from_openapi(openapi_spec)
            assert len(test_cases) > 0
            
            # 运行测试套件
            report = await tester.run_test_suite(test_cases)
            
            # 验证测试报告
            assert report.total_tests > 0
            assert report.success_rate > 0
            
            logger.info(f"Contract tests: {report.passed_tests}/{report.total_tests} passed ({report.success_rate:.1f}%)")
            
            await tester.close()
            
            logger.info("✅ Contract testing test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Contract testing test failed: {e}")
            return False
    
    async def test_service_governance(self) -> bool:
        """测试服务治理功能"""
        logger.info("Testing service governance...")
        
        try:
            # 注册服务实例
            instance = ServiceInstance(
                service_name="test_service",
                instance_id="test_instance_1",
                host="127.0.0.1",
                port=8999,
                version="1.0.0",
                status=ServiceStatus.HEALTHY
            )
            
            success = await self.service_registry.register_service(instance)
            assert success is True
            
            # 发现服务
            instances = await self.service_registry.discover_services("test_service")
            assert len(instances) > 0
            assert instances[0].instance_id == "test_instance_1"
            
            # 发送心跳
            heartbeat_success = await self.service_registry.heartbeat("test_service", "test_instance_1")
            assert heartbeat_success is True
            
            # 获取服务实例（负载均衡）
            selected_instance = await self.service_registry.get_service_instance("test_service")
            assert selected_instance is not None
            assert selected_instance.instance_id == "test_instance_1"
            
            # 注销服务
            deregister_success = await self.service_registry.deregister_service("test_service", "test_instance_1")
            assert deregister_success is True
            
            logger.info("✅ Service governance test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Service governance test failed: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        logger.info("Starting service boundary tests...")
        
        await self.setup()
        
        test_results = {}
        
        try:
            # 运行各项测试
            test_results["api_framework"] = await self.test_api_framework()
            test_results["service_client"] = await self.test_service_client()
            test_results["contract_testing"] = await self.test_contract_testing()
            test_results["service_governance"] = await self.test_service_governance()
            
        finally:
            await self.cleanup()
        
        # 输出测试结果
        logger.info("\n" + "="*50)
        logger.info("SERVICE BOUNDARY TEST RESULTS")
        logger.info("="*50)
        
        passed_count = 0
        for test_name, result in test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
            if result:
                passed_count += 1
        
        logger.info(f"\nOverall: {passed_count}/{len(test_results)} tests passed")
        
        if passed_count == len(test_results):
            logger.info("🎉 All service boundary tests passed!")
        else:
            logger.error("⚠️  Some service boundary tests failed!")
        
        return test_results


async def main():
    """主函数"""
    tester = ServiceBoundaryTester()
    results = await tester.run_all_tests()
    
    # 返回退出码
    all_passed = all(results.values())
    exit_code = 0 if all_passed else 1
    
    return exit_code


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
