#!/usr/bin/env python3
"""
异步处理架构演示脚本

展示异步处理架构的完整功能：
- 启动事件总线和任务队列
- 演示文档处理流水线
- 实时监控处理状态
- 展示错误处理和重试机制
"""

import asyncio
import json
import logging
import os
import tempfile
import time
import uuid
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到Python路径
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))

from shared.event_bus import EventBus, Event, EventType, get_event_bus, publish_event
from shared.task_queue import TaskQueue, Task, TaskStatus, get_task_queue
from shared.document_pipeline import setup_document_pipeline, start_document_processing

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AsyncProcessingDemo:
    """异步处理架构演示"""
    
    def __init__(self):
        self.event_bus = None
        self.task_queue = None
        self.running = False
    
    async def setup(self):
        """设置演示环境"""
        logger.info("🚀 Setting up async processing demo...")
        
        # 初始化事件总线
        self.event_bus = get_event_bus()
        await self.event_bus.connect()
        
        # 初始化任务队列
        self.task_queue = get_task_queue()
        await self.task_queue.connect()
        
        # 设置文档处理流水线
        await setup_document_pipeline(self.event_bus, self.task_queue)
        
        # 启动消费者和工作进程
        await self.event_bus.start_consuming()
        await self.task_queue.start_workers(num_workers=3)
        
        self.running = True
        logger.info("✅ Demo environment setup completed")
    
    async def cleanup(self):
        """清理演示环境"""
        logger.info("🧹 Cleaning up demo environment...")
        
        self.running = False
        
        if self.task_queue:
            await self.task_queue.stop_workers()
            await self.task_queue.disconnect()
        
        if self.event_bus:
            await self.event_bus.stop_consuming()
            await self.event_bus.disconnect()
        
        logger.info("✅ Demo environment cleanup completed")
    
    async def create_sample_documents(self) -> List[str]:
        """创建示例文档"""
        logger.info("📄 Creating sample documents...")
        
        documents = [
            {
                "name": "技术文档.txt",
                "content": """
# 异步处理架构技术文档

## 概述
本文档介绍了基于Redis的异步处理架构设计。

## 核心组件
1. 事件总线 - 负责事件的发布和订阅
2. 任务队列 - 负责任务的调度和执行
3. 文档处理流水线 - 负责文档的解析、分块、向量化和索引

## 特性
- 高性能异步处理
- 自动重试机制
- 优先级队列
- 事件驱动架构
- 可扩展的工作进程

## 应用场景
适用于需要处理大量文档和数据的AI应用系统。
                """.strip()
            },
            {
                "name": "用户手册.txt",
                "content": """
# 用户操作手册

## 快速开始
1. 上传文档到系统
2. 系统自动解析文档内容
3. 将文档分块处理
4. 生成向量表示
5. 建立搜索索引

## 功能特性
- 支持多种文档格式
- 智能文档分块
- 高效向量搜索
- 实时处理状态

## 注意事项
- 文档大小限制为10MB
- 支持的格式：txt, md, pdf, docx
- 处理时间取决于文档大小和复杂度
                """.strip()
            },
            {
                "name": "API文档.txt",
                "content": """
# API接口文档

## 文档上传接口
POST /api/v1/documents/upload
- 支持多文件上传
- 异步处理模式
- 返回任务ID用于状态查询

## 任务状态查询
GET /api/v1/tasks/{task_id}
- 查询任务处理状态
- 获取处理结果
- 错误信息反馈

## 搜索接口
POST /api/v1/search
- 语义搜索功能
- 支持向量相似度搜索
- 返回相关文档片段

## 健康检查
GET /api/v1/health
- 服务健康状态
- 依赖服务检查
- 系统资源监控
                """.strip()
            }
        ]
        
        file_paths = []
        for doc in documents:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                f.write(doc["content"])
                file_paths.append(f.name)
                logger.info(f"Created document: {doc['name']} -> {f.name}")
        
        return file_paths
    
    async def demo_document_processing(self, file_paths: List[str]):
        """演示文档处理流水线"""
        logger.info("🔄 Starting document processing pipeline demo...")
        
        processing_tasks = []
        
        for i, file_path in enumerate(file_paths):
            document_id = f"demo_doc_{i+1}_{uuid.uuid4().hex[:8]}"
            user_id = f"demo_user_{i+1}"
            
            logger.info(f"📤 Starting processing for document {i+1}: {document_id}")
            
            # 启动文档处理
            task_id = await start_document_processing(
                document_id=document_id,
                file_path=file_path,
                user_id=user_id
            )
            
            processing_tasks.append({
                "document_id": document_id,
                "task_id": task_id,
                "file_path": file_path,
                "user_id": user_id
            })
            
            # 间隔提交，避免同时处理
            await asyncio.sleep(2)
        
        return processing_tasks
    
    async def monitor_processing(self, processing_tasks: List[Dict]):
        """监控处理进度"""
        logger.info("📊 Monitoring processing progress...")
        
        completed_tasks = set()
        max_wait_time = 60  # 最大等待时间
        start_time = time.time()
        
        while len(completed_tasks) < len(processing_tasks) and time.time() - start_time < max_wait_time:
            logger.info(f"⏳ Checking progress... ({len(completed_tasks)}/{len(processing_tasks)} completed)")
            
            for task_info in processing_tasks:
                task_id = task_info["task_id"]
                document_id = task_info["document_id"]
                
                if task_id not in completed_tasks:
                    status = await self.task_queue.get_task_status(task_id)
                    task = await self.task_queue.get_task(task_id)
                    
                    if status == TaskStatus.SUCCESS:
                        completed_tasks.add(task_id)
                        logger.info(f"✅ Document {document_id} processing completed successfully")
                    elif status == TaskStatus.FAILURE:
                        completed_tasks.add(task_id)
                        error_msg = task.metadata.error_message if task else "Unknown error"
                        logger.error(f"❌ Document {document_id} processing failed: {error_msg}")
                    elif status == TaskStatus.RUNNING:
                        logger.info(f"🔄 Document {document_id} is still processing...")
                    elif status == TaskStatus.RETRY:
                        retry_count = task.metadata.retry_count if task else 0
                        logger.warning(f"🔄 Document {document_id} is retrying (attempt {retry_count})...")
            
            await asyncio.sleep(3)
        
        # 最终状态报告
        logger.info("\n" + "="*50)
        logger.info("PROCESSING RESULTS SUMMARY")
        logger.info("="*50)
        
        success_count = 0
        for task_info in processing_tasks:
            task_id = task_info["task_id"]
            document_id = task_info["document_id"]
            status = await self.task_queue.get_task_status(task_id)
            
            if status == TaskStatus.SUCCESS:
                success_count += 1
                logger.info(f"✅ {document_id}: SUCCESS")
            elif status == TaskStatus.FAILURE:
                logger.info(f"❌ {document_id}: FAILURE")
            else:
                logger.info(f"⏳ {document_id}: {status}")
        
        logger.info(f"\nOverall: {success_count}/{len(processing_tasks)} documents processed successfully")
        
        return success_count == len(processing_tasks)
    
    async def demo_event_publishing(self):
        """演示事件发布功能"""
        logger.info("📡 Demonstrating event publishing...")
        
        # 发布各种类型的事件
        events = [
            (EventType.USER_CREATED, {"user_id": "demo_user_1", "username": "demo_user"}),
            (EventType.TOPIC_CREATED, {"topic_id": "demo_topic_1", "title": "Demo Topic"}),
            (EventType.CONVERSATION_STARTED, {"conversation_id": "demo_conv_1", "user_id": "demo_user_1"}),
        ]
        
        for event_type, payload in events:
            await publish_event(
                event_type=event_type,
                payload=payload,
                source_service="demo_service"
            )
            logger.info(f"📤 Published event: {event_type}")
            await asyncio.sleep(1)
    
    async def demo_task_priorities(self):
        """演示任务优先级功能"""
        logger.info("⚡ Demonstrating task priorities...")
        
        from shared.task_queue import TaskPriority
        
        # 创建不同优先级的任务
        tasks = [
            Task.create("test.simple", {"data": "low_priority"}, priority=TaskPriority.LOW),
            Task.create("test.simple", {"data": "normal_priority"}, priority=TaskPriority.NORMAL),
            Task.create("test.simple", {"data": "high_priority"}, priority=TaskPriority.HIGH),
            Task.create("test.simple", {"data": "critical_priority"}, priority=TaskPriority.CRITICAL),
        ]
        
        # 反向提交（低优先级先提交）
        for task in reversed(tasks):
            await self.task_queue.enqueue(task)
            logger.info(f"📤 Enqueued {task.metadata.priority.name} priority task")
        
        await asyncio.sleep(5)
        logger.info("✅ Priority demonstration completed")
    
    async def run_demo(self):
        """运行完整演示"""
        logger.info("🎬 Starting Async Processing Architecture Demo")
        logger.info("="*60)
        
        try:
            # 设置环境
            await self.setup()
            
            # 创建示例文档
            file_paths = await self.create_sample_documents()
            
            try:
                # 演示事件发布
                await self.demo_event_publishing()
                await asyncio.sleep(2)
                
                # 演示任务优先级
                await self.demo_task_priorities()
                await asyncio.sleep(2)
                
                # 演示文档处理流水线
                processing_tasks = await self.demo_document_processing(file_paths)
                
                # 监控处理进度
                success = await self.monitor_processing(processing_tasks)
                
                # 最终结果
                if success:
                    logger.info("🎉 Demo completed successfully!")
                else:
                    logger.warning("⚠️  Demo completed with some issues")
                
            finally:
                # 清理示例文档
                for file_path in file_paths:
                    try:
                        os.unlink(file_path)
                    except:
                        pass
        
        except Exception as e:
            logger.error(f"❌ Demo failed: {e}")
            raise
        
        finally:
            await self.cleanup()
        
        logger.info("="*60)
        logger.info("🏁 Demo finished")


async def main():
    """主函数"""
    demo = AsyncProcessingDemo()
    
    try:
        await demo.run_demo()
    except KeyboardInterrupt:
        logger.info("\n⏹️  Demo interrupted by user")
    except Exception as e:
        logger.error(f"❌ Demo error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
