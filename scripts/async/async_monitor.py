#!/usr/bin/env python3
"""
异步处理监控工具

提供实时监控异步任务和事件处理的状态：
- 任务队列状态监控
- 事件流监控
- 性能指标统计
- 错误率分析
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque

import redis.asyncio as redis
from rich.console import Console
from rich.table import Table
from rich.live import Live
from rich.panel import Panel
from rich.layout import Layout
from rich.text import Text

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

console = Console()


class AsyncMonitor:
    """异步处理监控器"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis_client: Optional[redis.Redis] = None
        self.running = False
        
        # 统计数据
        self.task_stats = defaultdict(int)
        self.event_stats = defaultdict(int)
        self.error_stats = defaultdict(int)
        self.performance_history = deque(maxlen=100)
        
        # 监控间隔
        self.monitor_interval = 2.0
    
    async def connect(self):
        """连接到Redis"""
        self.redis_client = redis.from_url(self.redis_url)
        await self.redis_client.ping()
        logger.info(f"Connected to Redis: {self.redis_url}")
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Disconnected from Redis")
    
    async def start_monitoring(self):
        """开始监控"""
        if not self.redis_client:
            await self.connect()
        
        self.running = True
        
        # 创建监控布局
        layout = Layout()
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )
        
        layout["main"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        layout["left"].split_column(
            Layout(name="tasks"),
            Layout(name="events")
        )
        
        layout["right"].split_column(
            Layout(name="performance"),
            Layout(name="errors")
        )
        
        # 启动实时显示
        with Live(layout, refresh_per_second=1, screen=True):
            while self.running:
                try:
                    # 收集监控数据
                    await self._collect_stats()
                    
                    # 更新显示
                    self._update_layout(layout)
                    
                    await asyncio.sleep(self.monitor_interval)
                    
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    logger.error(f"Monitoring error: {e}")
                    await asyncio.sleep(1)
        
        self.running = False
    
    async def _collect_stats(self):
        """收集统计数据"""
        try:
            # 收集任务统计
            await self._collect_task_stats()
            
            # 收集事件统计
            await self._collect_event_stats()
            
            # 收集性能数据
            await self._collect_performance_stats()
            
        except Exception as e:
            logger.error(f"Error collecting stats: {e}")
    
    async def _collect_task_stats(self):
        """收集任务统计"""
        # 获取所有任务键
        task_keys = await self.redis_client.keys("tasks:task:*")
        
        # 重置统计
        self.task_stats.clear()
        
        for key in task_keys:
            try:
                task_data = await self.redis_client.hgetall(key)
                if task_data:
                    status = task_data.get(b'status', b'unknown').decode()
                    self.task_stats[status] += 1
            except Exception as e:
                logger.debug(f"Error reading task {key}: {e}")
    
    async def _collect_event_stats(self):
        """收集事件统计"""
        # 获取所有事件流
        stream_keys = await self.redis_client.keys("events:*")
        
        # 重置统计
        self.event_stats.clear()
        
        for key in stream_keys:
            try:
                stream_name = key.decode()
                event_type = stream_name.split(':')[-1]
                
                # 获取流长度
                length = await self.redis_client.xlen(key)
                self.event_stats[event_type] = length
                
            except Exception as e:
                logger.debug(f"Error reading stream {key}: {e}")
    
    async def _collect_performance_stats(self):
        """收集性能统计"""
        current_time = time.time()
        
        # 计算任务处理速率
        total_tasks = sum(self.task_stats.values())
        total_events = sum(self.event_stats.values())
        
        # 添加到历史记录
        self.performance_history.append({
            'timestamp': current_time,
            'total_tasks': total_tasks,
            'total_events': total_events,
            'success_rate': self._calculate_success_rate(),
            'error_rate': self._calculate_error_rate()
        })
    
    def _calculate_success_rate(self) -> float:
        """计算成功率"""
        total = sum(self.task_stats.values())
        if total == 0:
            return 0.0
        success = self.task_stats.get('success', 0)
        return (success / total) * 100
    
    def _calculate_error_rate(self) -> float:
        """计算错误率"""
        total = sum(self.task_stats.values())
        if total == 0:
            return 0.0
        errors = self.task_stats.get('failure', 0) + self.task_stats.get('timeout', 0)
        return (errors / total) * 100
    
    def _update_layout(self, layout):
        """更新显示布局"""
        # 更新标题
        layout["header"].update(
            Panel(
                Text("异步处理监控面板", style="bold blue", justify="center"),
                title=f"监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
        )
        
        # 更新任务统计
        layout["tasks"].update(self._create_task_table())
        
        # 更新事件统计
        layout["events"].update(self._create_event_table())
        
        # 更新性能统计
        layout["performance"].update(self._create_performance_panel())
        
        # 更新错误统计
        layout["errors"].update(self._create_error_panel())
        
        # 更新底部信息
        layout["footer"].update(
            Panel(
                Text("按 Ctrl+C 退出监控", style="dim", justify="center"),
                style="dim"
            )
        )
    
    def _create_task_table(self) -> Table:
        """创建任务统计表"""
        table = Table(title="任务队列状态", show_header=True, header_style="bold magenta")
        table.add_column("状态", style="cyan", width=12)
        table.add_column("数量", justify="right", style="green", width=8)
        table.add_column("百分比", justify="right", style="yellow", width=10)
        
        total = sum(self.task_stats.values())
        
        for status, count in sorted(self.task_stats.items()):
            percentage = (count / total * 100) if total > 0 else 0
            
            # 根据状态设置颜色
            status_style = "green" if status == "success" else "red" if status in ["failure", "timeout"] else "yellow"
            
            table.add_row(
                Text(status, style=status_style),
                str(count),
                f"{percentage:.1f}%"
            )
        
        if total == 0:
            table.add_row("无任务", "0", "0.0%")
        
        return table
    
    def _create_event_table(self) -> Table:
        """创建事件统计表"""
        table = Table(title="事件流状态", show_header=True, header_style="bold magenta")
        table.add_column("事件类型", style="cyan", width=20)
        table.add_column("队列长度", justify="right", style="green", width=10)
        
        for event_type, length in sorted(self.event_stats.items()):
            table.add_row(event_type, str(length))
        
        if not self.event_stats:
            table.add_row("无事件", "0")
        
        return table
    
    def _create_performance_panel(self) -> Panel:
        """创建性能统计面板"""
        if not self.performance_history:
            return Panel("暂无性能数据", title="性能统计")
        
        latest = self.performance_history[-1]
        
        # 计算趋势
        if len(self.performance_history) >= 2:
            prev = self.performance_history[-2]
            task_trend = latest['total_tasks'] - prev['total_tasks']
            event_trend = latest['total_events'] - prev['total_events']
        else:
            task_trend = 0
            event_trend = 0
        
        content = f"""
总任务数: {latest['total_tasks']} ({task_trend:+d})
总事件数: {latest['total_events']} ({event_trend:+d})
成功率: {latest['success_rate']:.1f}%
错误率: {latest['error_rate']:.1f}%
        """.strip()
        
        return Panel(content, title="性能统计", border_style="blue")
    
    def _create_error_panel(self) -> Panel:
        """创建错误统计面板"""
        error_count = self.task_stats.get('failure', 0) + self.task_stats.get('timeout', 0)
        retry_count = self.task_stats.get('retry', 0)
        
        content = f"""
失败任务: {error_count}
重试任务: {retry_count}
待处理: {self.task_stats.get('pending', 0)}
运行中: {self.task_stats.get('running', 0)}
        """.strip()
        
        style = "red" if error_count > 0 else "green"
        return Panel(content, title="错误统计", border_style=style)
    
    async def get_queue_info(self) -> Dict[str, Any]:
        """获取队列信息"""
        info = {
            'task_stats': dict(self.task_stats),
            'event_stats': dict(self.event_stats),
            'performance': list(self.performance_history)[-10:],  # 最近10条记录
            'timestamp': time.time()
        }
        return info
    
    async def clear_completed_tasks(self, older_than_hours: int = 24):
        """清理已完成的任务"""
        cutoff_time = time.time() - (older_than_hours * 3600)
        
        task_keys = await self.redis_client.keys("tasks:task:*")
        cleared_count = 0
        
        for key in task_keys:
            try:
                task_data = await self.redis_client.hgetall(key)
                if task_data:
                    status = task_data.get(b'status', b'').decode()
                    updated_at = float(task_data.get(b'updated_at', 0))
                    
                    # 清理已完成且超过指定时间的任务
                    if status in ['success', 'failure', 'cancelled'] and updated_at < cutoff_time:
                        await self.redis_client.delete(key)
                        cleared_count += 1
                        
            except Exception as e:
                logger.debug(f"Error clearing task {key}: {e}")
        
        logger.info(f"Cleared {cleared_count} completed tasks")
        return cleared_count


async def main():
    """主函数"""
    monitor = AsyncMonitor()
    
    try:
        await monitor.start_monitoring()
    except KeyboardInterrupt:
        console.print("\n[yellow]监控已停止[/yellow]")
    finally:
        await monitor.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
