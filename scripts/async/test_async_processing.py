#!/usr/bin/env python3
"""
异步处理测试脚本

测试异步处理架构的各个组件：
- 事件总线功能测试
- 任务队列功能测试
- 文档处理流水线测试
- 性能和并发测试
"""

import asyncio
import json
import logging
import os
import tempfile
import time
import uuid
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到Python路径
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))

from shared.event_bus import EventBus, Event, EventType, EventHandler, get_event_bus
from shared.task_queue import TaskQueue, Task, TaskProcessor, TaskStatus, get_task_queue
from shared.document_pipeline import setup_document_pipeline, start_document_processing

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestEventHandler(EventHandler):
    """测试事件处理器"""
    
    def __init__(self):
        super().__init__([EventType.DOCUMENT_UPLOADED, EventType.DOCUMENT_INDEXED])
        self.handled_events = []
    
    async def handle(self, event: Event) -> bool:
        """处理测试事件"""
        self.handled_events.append(event.metadata.event_id)
        logger.info(f"Handled event: {event.metadata.event_type} - {event.metadata.event_id}")
        return True


class TestTaskProcessor(TaskProcessor):
    """测试任务处理器"""
    
    def __init__(self):
        super().__init__(["test.simple", "test.slow", "test.error"])
        self.processed_tasks = []
    
    async def process(self, task: Task) -> Dict[str, Any]:
        """处理测试任务"""
        task_type = task.metadata.task_type
        
        if task_type == "test.simple":
            await asyncio.sleep(0.1)
            result = {"message": "Simple task completed", "input": task.payload}
            
        elif task_type == "test.slow":
            await asyncio.sleep(2.0)
            result = {"message": "Slow task completed", "duration": 2.0}
            
        elif task_type == "test.error":
            if task.metadata.retry_count < 2:
                raise ValueError("Simulated error for testing retry")
            result = {"message": "Error task completed after retries"}
        
        else:
            raise ValueError(f"Unknown task type: {task_type}")
        
        self.processed_tasks.append(task.metadata.task_id)
        logger.info(f"Processed task: {task_type} - {task.metadata.task_id}")
        return result


class AsyncProcessingTester:
    """异步处理测试器"""
    
    def __init__(self):
        self.event_bus = None
        self.task_queue = None
        self.test_handler = TestEventHandler()
        self.test_processor = TestTaskProcessor()
    
    async def setup(self):
        """设置测试环境"""
        logger.info("Setting up test environment...")
        
        # 初始化事件总线
        self.event_bus = get_event_bus()
        await self.event_bus.connect()
        self.event_bus.register_handler(self.test_handler)
        
        # 初始化任务队列
        self.task_queue = get_task_queue()
        await self.task_queue.connect()
        self.task_queue.register_processor(self.test_processor)
        
        # 设置文档处理流水线
        await setup_document_pipeline(self.event_bus, self.task_queue)
        
        logger.info("Test environment setup completed")
    
    async def cleanup(self):
        """清理测试环境"""
        logger.info("Cleaning up test environment...")
        
        if self.task_queue:
            await self.task_queue.stop_workers()
            await self.task_queue.disconnect()
        
        if self.event_bus:
            await self.event_bus.stop_consuming()
            await self.event_bus.disconnect()
        
        logger.info("Test environment cleanup completed")
    
    async def test_event_bus(self) -> bool:
        """测试事件总线功能"""
        logger.info("Testing event bus...")
        
        try:
            # 启动事件消费
            await self.event_bus.start_consuming()
            await asyncio.sleep(1)  # 等待消费者启动
            
            # 发布测试事件
            test_event = Event.create(
                event_type=EventType.DOCUMENT_UPLOADED,
                payload={"test": "event_bus_test", "document_id": "test_doc_1"},
                source_service="test_service"
            )
            
            await self.event_bus.publish(test_event)
            
            # 等待事件处理
            await asyncio.sleep(2)
            
            # 验证事件是否被处理
            if test_event.metadata.event_id in self.test_handler.handled_events:
                logger.info("✅ Event bus test passed")
                return True
            else:
                logger.error("❌ Event bus test failed: Event not handled")
                return False
                
        except Exception as e:
            logger.error(f"❌ Event bus test failed: {e}")
            return False
    
    async def test_task_queue(self) -> bool:
        """测试任务队列功能"""
        logger.info("Testing task queue...")
        
        try:
            # 启动工作进程
            await self.task_queue.start_workers(num_workers=2)
            await asyncio.sleep(1)  # 等待工作进程启动
            
            # 创建测试任务
            tasks = [
                Task.create("test.simple", {"data": "test1"}),
                Task.create("test.simple", {"data": "test2"}),
                Task.create("test.slow", {"data": "slow_test"}),
            ]
            
            # 提交任务
            task_ids = []
            for task in tasks:
                task_id = await self.task_queue.enqueue(task)
                task_ids.append(task_id)
            
            # 等待任务完成
            await asyncio.sleep(5)
            
            # 检查任务状态
            completed_count = 0
            for task_id in task_ids:
                status = await self.task_queue.get_task_status(task_id)
                if status == TaskStatus.SUCCESS:
                    completed_count += 1
            
            if completed_count == len(tasks):
                logger.info("✅ Task queue test passed")
                return True
            else:
                logger.error(f"❌ Task queue test failed: {completed_count}/{len(tasks)} tasks completed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Task queue test failed: {e}")
            return False
    
    async def test_retry_mechanism(self) -> bool:
        """测试重试机制"""
        logger.info("Testing retry mechanism...")
        
        try:
            # 创建会失败的任务
            error_task = Task.create("test.error", {"data": "error_test"}, max_retries=3)
            task_id = await self.task_queue.enqueue(error_task)
            
            # 等待任务完成（包括重试）
            await asyncio.sleep(10)
            
            # 检查任务状态
            final_task = await self.task_queue.get_task(task_id)
            if final_task and final_task.metadata.status == TaskStatus.SUCCESS:
                logger.info("✅ Retry mechanism test passed")
                return True
            else:
                logger.error("❌ Retry mechanism test failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Retry mechanism test failed: {e}")
            return False
    
    async def test_document_pipeline(self) -> bool:
        """测试文档处理流水线"""
        logger.info("Testing document processing pipeline...")
        
        try:
            # 创建测试文件
            test_content = "这是一个测试文档。\n\n它包含多个段落和一些测试内容。\n\n用于验证文档处理流水线的功能。"
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                f.write(test_content)
                test_file_path = f.name
            
            try:
                # 启动文档处理
                document_id = f"test_doc_{uuid.uuid4().hex[:8]}"
                task_id = await start_document_processing(
                    document_id=document_id,
                    file_path=test_file_path,
                    user_id="test_user"
                )
                
                logger.info(f"Started document processing: {document_id}, task_id: {task_id}")
                
                # 等待处理完成
                await asyncio.sleep(15)
                
                # 检查是否有索引完成事件
                indexed_events = [
                    event_id for event_id in self.test_handler.handled_events
                    if any(event.metadata.event_type == EventType.DOCUMENT_INDEXED 
                          for event in [Event.create(EventType.DOCUMENT_INDEXED, {}, "test")])
                ]
                
                # 简化验证：检查任务是否完成
                task = await self.task_queue.get_task(task_id)
                if task and task.metadata.status == TaskStatus.SUCCESS:
                    logger.info("✅ Document pipeline test passed")
                    return True
                else:
                    logger.error("❌ Document pipeline test failed")
                    return False
                    
            finally:
                # 清理测试文件
                os.unlink(test_file_path)
                
        except Exception as e:
            logger.error(f"❌ Document pipeline test failed: {e}")
            return False
    
    async def test_concurrent_processing(self) -> bool:
        """测试并发处理能力"""
        logger.info("Testing concurrent processing...")
        
        try:
            # 创建大量并发任务
            num_tasks = 20
            tasks = []
            
            for i in range(num_tasks):
                task = Task.create("test.simple", {"data": f"concurrent_test_{i}"})
                tasks.append(task)
            
            # 记录开始时间
            start_time = time.time()
            
            # 并发提交任务
            task_ids = await asyncio.gather(*[
                self.task_queue.enqueue(task) for task in tasks
            ])
            
            # 等待所有任务完成
            max_wait_time = 30
            wait_start = time.time()
            
            while time.time() - wait_start < max_wait_time:
                completed_count = 0
                for task_id in task_ids:
                    status = await self.task_queue.get_task_status(task_id)
                    if status in [TaskStatus.SUCCESS, TaskStatus.FAILURE]:
                        completed_count += 1
                
                if completed_count == num_tasks:
                    break
                
                await asyncio.sleep(1)
            
            # 计算处理时间
            total_time = time.time() - start_time
            
            # 统计成功任务数
            success_count = 0
            for task_id in task_ids:
                status = await self.task_queue.get_task_status(task_id)
                if status == TaskStatus.SUCCESS:
                    success_count += 1
            
            logger.info(f"Concurrent processing: {success_count}/{num_tasks} tasks completed in {total_time:.2f}s")
            
            if success_count >= num_tasks * 0.9:  # 90%成功率
                logger.info("✅ Concurrent processing test passed")
                return True
            else:
                logger.error("❌ Concurrent processing test failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Concurrent processing test failed: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        logger.info("Starting async processing tests...")
        
        await self.setup()
        
        test_results = {}
        
        try:
            # 运行各项测试
            test_results["event_bus"] = await self.test_event_bus()
            test_results["task_queue"] = await self.test_task_queue()
            test_results["retry_mechanism"] = await self.test_retry_mechanism()
            test_results["document_pipeline"] = await self.test_document_pipeline()
            test_results["concurrent_processing"] = await self.test_concurrent_processing()
            
        finally:
            await self.cleanup()
        
        # 输出测试结果
        logger.info("\n" + "="*50)
        logger.info("ASYNC PROCESSING TEST RESULTS")
        logger.info("="*50)
        
        passed_count = 0
        for test_name, result in test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
            if result:
                passed_count += 1
        
        logger.info(f"\nOverall: {passed_count}/{len(test_results)} tests passed")
        
        if passed_count == len(test_results):
            logger.info("🎉 All async processing tests passed!")
        else:
            logger.error("⚠️  Some async processing tests failed!")
        
        return test_results


async def main():
    """主函数"""
    tester = AsyncProcessingTester()
    results = await tester.run_all_tests()
    
    # 返回退出码
    all_passed = all(results.values())
    exit_code = 0 if all_passed else 1
    
    return exit_code


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
