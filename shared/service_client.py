"""
服务间通信客户端 - 标准化的服务调用框架

提供：
- 统一的HTTP客户端
- 自动重试和熔断机制
- 服务发现和负载均衡
- 请求追踪和监控
- 错误处理和降级
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import dataclass

import httpx
from pydantic import BaseModel

from .api_framework import APIVersion, ErrorCode, BaseAPIResponse, APIError

logger = logging.getLogger(__name__)


class CircuitState(str, Enum):
    """熔断器状态"""
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态


@dataclass
class ServiceEndpoint:
    """服务端点信息"""
    name: str
    base_url: str
    health_check_path: str = "/api/v1/health"
    timeout: float = 30.0
    max_retries: int = 3
    circuit_breaker_enabled: bool = True


@dataclass
class RequestMetrics:
    """请求指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    last_request_time: Optional[float] = None


class CircuitBreaker:
    """熔断器"""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        expected_exception: type = Exception
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time: Optional[float] = None
        self.state = CircuitState.CLOSED
    
    def can_execute(self) -> bool:
        """检查是否可以执行请求"""
        if self.state == CircuitState.CLOSED:
            return True
        
        if self.state == CircuitState.OPEN:
            if self.last_failure_time and time.time() - self.last_failure_time >= self.recovery_timeout:
                self.state = CircuitState.HALF_OPEN
                return True
            return False
        
        # HALF_OPEN state
        return True
    
    def record_success(self):
        """记录成功请求"""
        self.failure_count = 0
        self.state = CircuitState.CLOSED
    
    def record_failure(self):
        """记录失败请求"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN


class ServiceClient:
    """服务客户端"""
    
    def __init__(
        self,
        service_name: str,
        correlation_id: Optional[str] = None,
        user_id: Optional[str] = None
    ):
        self.service_name = service_name
        self.correlation_id = correlation_id or str(uuid.uuid4())
        self.user_id = user_id
        
        self.endpoints: Dict[str, ServiceEndpoint] = {}
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.metrics: Dict[str, RequestMetrics] = {}
        
        # HTTP客户端配置
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            limits=httpx.Limits(max_connections=100, max_keepalive_connections=20)
        )
    
    def register_endpoint(self, endpoint: ServiceEndpoint):
        """注册服务端点"""
        self.endpoints[endpoint.name] = endpoint
        self.circuit_breakers[endpoint.name] = CircuitBreaker()
        self.metrics[endpoint.name] = RequestMetrics()
        
        logger.info(f"Registered endpoint: {endpoint.name} -> {endpoint.base_url}")
    
    async def call(
        self,
        service_name: str,
        method: str,
        path: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        api_version: APIVersion = APIVersion.V1,
        timeout: Optional[float] = None
    ) -> Dict[str, Any]:
        """调用服务接口"""
        
        endpoint = self.endpoints.get(service_name)
        if not endpoint:
            raise ValueError(f"Service endpoint not found: {service_name}")
        
        circuit_breaker = self.circuit_breakers[service_name]
        
        # 检查熔断器状态
        if not circuit_breaker.can_execute():
            raise Exception(f"Circuit breaker is open for service: {service_name}")
        
        # 构建请求
        url = f"{endpoint.base_url.rstrip('/')}/api/{api_version.value}/{path.lstrip('/')}"
        request_headers = self._build_headers(headers)
        request_timeout = timeout or endpoint.timeout
        
        start_time = time.time()
        
        try:
            # 执行请求
            response = await self._execute_request(
                method=method,
                url=url,
                data=data,
                params=params,
                headers=request_headers,
                timeout=request_timeout,
                max_retries=endpoint.max_retries
            )
            
            # 记录成功
            circuit_breaker.record_success()
            self._record_success(service_name, time.time() - start_time)
            
            return response
            
        except Exception as e:
            # 记录失败
            circuit_breaker.record_failure()
            self._record_failure(service_name)
            
            logger.error(f"Service call failed: {service_name} {method} {path} - {e}")
            raise
    
    async def get(
        self,
        service_name: str,
        path: str,
        params: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """GET请求"""
        return await self.call(service_name, "GET", path, params=params, **kwargs)
    
    async def post(
        self,
        service_name: str,
        path: str,
        data: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """POST请求"""
        return await self.call(service_name, "POST", path, data=data, **kwargs)
    
    async def put(
        self,
        service_name: str,
        path: str,
        data: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """PUT请求"""
        return await self.call(service_name, "PUT", path, data=data, **kwargs)
    
    async def delete(
        self,
        service_name: str,
        path: str,
        **kwargs
    ) -> Dict[str, Any]:
        """DELETE请求"""
        return await self.call(service_name, "DELETE", path, **kwargs)
    
    async def health_check(self, service_name: str) -> bool:
        """健康检查"""
        try:
            endpoint = self.endpoints.get(service_name)
            if not endpoint:
                return False
            
            url = f"{endpoint.base_url.rstrip('/')}{endpoint.health_check_path}"
            response = await self.client.get(url, timeout=5.0)
            
            return response.status_code == 200
            
        except Exception as e:
            logger.warning(f"Health check failed for {service_name}: {e}")
            return False
    
    def _build_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """构建请求头"""
        headers = {
            "Content-Type": "application/json",
            "X-Request-ID": str(uuid.uuid4()),
            "X-Correlation-ID": self.correlation_id,
            "X-Service-Name": self.service_name,
            "User-Agent": f"ServiceClient/{self.service_name}"
        }
        
        if self.user_id:
            headers["X-User-ID"] = self.user_id
        
        if additional_headers:
            headers.update(additional_headers)
        
        return headers
    
    async def _execute_request(
        self,
        method: str,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        timeout: float = 30.0,
        max_retries: int = 3
    ) -> Dict[str, Any]:
        """执行HTTP请求"""
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                # 准备请求参数
                request_kwargs = {
                    "url": url,
                    "headers": headers,
                    "params": params,
                    "timeout": timeout
                }
                
                if data is not None:
                    request_kwargs["json"] = data
                
                # 执行请求
                response = await self.client.request(method, **request_kwargs)
                
                # 检查响应状态
                if response.status_code >= 400:
                    error_data = {}
                    try:
                        error_data = response.json()
                    except:
                        error_data = {"message": response.text}
                    
                    raise httpx.HTTPStatusError(
                        message=f"HTTP {response.status_code}",
                        request=response.request,
                        response=response
                    )
                
                # 解析响应
                try:
                    return response.json()
                except json.JSONDecodeError:
                    return {"message": response.text}
                
            except Exception as e:
                last_exception = e
                
                if attempt < max_retries:
                    # 计算重试延迟（指数退避）
                    delay = min(2 ** attempt, 10)
                    logger.warning(f"Request failed, retrying in {delay}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"Request failed after {max_retries} retries: {e}")
        
        # 所有重试都失败了
        raise last_exception
    
    def _record_success(self, service_name: str, response_time: float):
        """记录成功请求"""
        metrics = self.metrics[service_name]
        metrics.total_requests += 1
        metrics.successful_requests += 1
        metrics.last_request_time = time.time()
        
        # 更新平均响应时间
        if metrics.average_response_time == 0:
            metrics.average_response_time = response_time
        else:
            metrics.average_response_time = (
                metrics.average_response_time * 0.9 + response_time * 0.1
            )
    
    def _record_failure(self, service_name: str):
        """记录失败请求"""
        metrics = self.metrics[service_name]
        metrics.total_requests += 1
        metrics.failed_requests += 1
        metrics.last_request_time = time.time()
    
    def get_metrics(self, service_name: str) -> Optional[RequestMetrics]:
        """获取请求指标"""
        return self.metrics.get(service_name)
    
    def get_circuit_breaker_state(self, service_name: str) -> Optional[CircuitState]:
        """获取熔断器状态"""
        circuit_breaker = self.circuit_breakers.get(service_name)
        return circuit_breaker.state if circuit_breaker else None
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


class ServiceClientManager:
    """服务客户端管理器"""
    
    def __init__(self):
        self.clients: Dict[str, ServiceClient] = {}
        self.endpoints: Dict[str, ServiceEndpoint] = {}
    
    def register_endpoint(self, endpoint: ServiceEndpoint):
        """注册服务端点"""
        self.endpoints[endpoint.name] = endpoint
        logger.info(f"Registered service endpoint: {endpoint.name}")
    
    def get_client(
        self,
        service_name: str,
        correlation_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> ServiceClient:
        """获取服务客户端"""
        client_key = f"{service_name}:{correlation_id or 'default'}"
        
        if client_key not in self.clients:
            client = ServiceClient(service_name, correlation_id, user_id)
            
            # 注册所有端点
            for endpoint in self.endpoints.values():
                client.register_endpoint(endpoint)
            
            self.clients[client_key] = client
        
        return self.clients[client_key]
    
    async def health_check_all(self) -> Dict[str, bool]:
        """检查所有服务健康状态"""
        results = {}
        
        # 创建临时客户端进行健康检查
        temp_client = ServiceClient("health_checker")
        for endpoint in self.endpoints.values():
            temp_client.register_endpoint(endpoint)
        
        try:
            for service_name in self.endpoints.keys():
                results[service_name] = await temp_client.health_check(service_name)
        finally:
            await temp_client.close()
        
        return results
    
    async def close_all(self):
        """关闭所有客户端"""
        for client in self.clients.values():
            await client.close()
        self.clients.clear()


# 全局服务客户端管理器
_service_client_manager = ServiceClientManager()


def get_service_client_manager() -> ServiceClientManager:
    """获取服务客户端管理器"""
    return _service_client_manager


def get_service_client(
    service_name: str,
    correlation_id: Optional[str] = None,
    user_id: Optional[str] = None
) -> ServiceClient:
    """获取服务客户端"""
    return _service_client_manager.get_client(service_name, correlation_id, user_id)
