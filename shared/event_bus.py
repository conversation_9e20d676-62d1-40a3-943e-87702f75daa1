"""
事件总线框架 - 支持事件驱动的异步处理架构

基于Redis Streams实现的高性能事件总线，支持：
- 事件发布和订阅
- 消息持久化和重试
- 消费者组管理
- 事件路由和过滤
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass, asdict
from contextlib import asynccontextmanager

import redis.asyncio as redis
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class EventType(str, Enum):
    """事件类型枚举"""
    # 文档处理事件
    DOCUMENT_UPLOADED = "document.uploaded"
    DOCUMENT_PARSED = "document.parsed"
    DOCUMENT_CHUNKED = "document.chunked"
    DOCUMENT_VECTORIZED = "document.vectorized"
    DOCUMENT_INDEXED = "document.indexed"
    DOCUMENT_PROCESSING_FAILED = "document.processing_failed"
    
    # 用户事件
    USER_CREATED = "user.created"
    USER_UPDATED = "user.updated"
    
    # 主题事件
    TOPIC_CREATED = "topic.created"
    TOPIC_UPDATED = "topic.updated"
    TOPIC_DELETED = "topic.deleted"
    
    # 对话事件
    CONVERSATION_STARTED = "conversation.started"
    MESSAGE_SENT = "message.sent"
    MESSAGE_RECEIVED = "message.received"
    
    # 摘要事件
    SUMMARY_REQUESTED = "summary.requested"
    SUMMARY_GENERATED = "summary.generated"


class EventPriority(int, Enum):
    """事件优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class EventMetadata:
    """事件元数据"""
    event_id: str
    event_type: EventType
    timestamp: float
    source_service: str
    correlation_id: Optional[str] = None
    user_id: Optional[str] = None
    priority: EventPriority = EventPriority.NORMAL
    retry_count: int = 0
    max_retries: int = 3
    delay_seconds: int = 0


class Event(BaseModel):
    """事件基类"""
    metadata: EventMetadata
    payload: Dict[str, Any] = Field(default_factory=dict)
    
    @classmethod
    def create(
        cls,
        event_type: EventType,
        payload: Dict[str, Any],
        source_service: str,
        user_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        priority: EventPriority = EventPriority.NORMAL
    ) -> "Event":
        """创建事件实例"""
        return cls(
            metadata=EventMetadata(
                event_id=str(uuid.uuid4()),
                event_type=event_type,
                timestamp=time.time(),
                source_service=source_service,
                correlation_id=correlation_id or str(uuid.uuid4()),
                user_id=user_id,
                priority=priority
            ),
            payload=payload
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "metadata": asdict(self.metadata),
            "payload": self.payload
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Event":
        """从字典创建事件实例"""
        metadata_data = data["metadata"]
        metadata = EventMetadata(
            event_id=metadata_data["event_id"],
            event_type=EventType(metadata_data["event_type"]),
            timestamp=metadata_data["timestamp"],
            source_service=metadata_data["source_service"],
            correlation_id=metadata_data.get("correlation_id"),
            user_id=metadata_data.get("user_id"),
            priority=EventPriority(metadata_data.get("priority", EventPriority.NORMAL)),
            retry_count=metadata_data.get("retry_count", 0),
            max_retries=metadata_data.get("max_retries", 3),
            delay_seconds=metadata_data.get("delay_seconds", 0)
        )
        return cls(metadata=metadata, payload=data.get("payload", {}))


class EventHandler:
    """事件处理器基类"""
    
    def __init__(self, event_types: List[EventType]):
        self.event_types = event_types
    
    async def handle(self, event: Event) -> bool:
        """
        处理事件
        
        Args:
            event: 要处理的事件
            
        Returns:
            bool: 处理是否成功
        """
        raise NotImplementedError
    
    async def on_error(self, event: Event, error: Exception) -> bool:
        """
        错误处理回调
        
        Args:
            event: 处理失败的事件
            error: 错误信息
            
        Returns:
            bool: 是否需要重试
        """
        logger.error(f"Event handling failed: {error}, event: {event.metadata.event_id}")
        return True  # 默认重试


class EventBus:
    """事件总线"""
    
    def __init__(
        self,
        redis_url: str = "redis://localhost:6379",
        stream_prefix: str = "events",
        consumer_group: str = "default",
        consumer_name: Optional[str] = None
    ):
        self.redis_url = redis_url
        self.stream_prefix = stream_prefix
        self.consumer_group = consumer_group
        self.consumer_name = consumer_name or f"consumer-{uuid.uuid4().hex[:8]}"
        
        self.redis_client: Optional[redis.Redis] = None
        self.handlers: Dict[EventType, List[EventHandler]] = {}
        self.running = False
        self._consumer_tasks: List[asyncio.Task] = []
    
    async def connect(self):
        """连接到Redis"""
        self.redis_client = redis.from_url(self.redis_url)
        await self.redis_client.ping()
        logger.info(f"Connected to Redis: {self.redis_url}")
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("Disconnected from Redis")
    
    def register_handler(self, handler: EventHandler):
        """注册事件处理器"""
        for event_type in handler.event_types:
            if event_type not in self.handlers:
                self.handlers[event_type] = []
            self.handlers[event_type].append(handler)
        logger.info(f"Registered handler for events: {handler.event_types}")
    
    async def publish(self, event: Event) -> str:
        """发布事件"""
        if not self.redis_client:
            raise RuntimeError("EventBus not connected")

        stream_name = f"{self.stream_prefix}:{event.metadata.event_type.value}"
        event_data = event.to_dict()

        # 添加优先级处理
        if event.metadata.priority == EventPriority.CRITICAL:
            stream_name = f"{stream_name}:critical"
        elif event.metadata.priority == EventPriority.HIGH:
            stream_name = f"{stream_name}:high"

        # 将事件数据序列化为字符串
        serialized_data = {
            "event_data": json.dumps(event_data)
        }

        message_id = await self.redis_client.xadd(
            stream_name,
            serialized_data,
            maxlen=10000  # 限制流长度
        )

        logger.info(f"Published event {event.metadata.event_id} to {stream_name}")
        return message_id
    
    async def start_consuming(self):
        """开始消费事件"""
        if not self.redis_client:
            raise RuntimeError("EventBus not connected")
        
        self.running = True
        
        # 为每个事件类型创建消费者任务
        for event_type in self.handlers.keys():
            task = asyncio.create_task(self._consume_stream(event_type))
            self._consumer_tasks.append(task)
        
        logger.info(f"Started consuming events with {len(self._consumer_tasks)} consumers")
    
    async def stop_consuming(self):
        """停止消费事件"""
        self.running = False
        
        # 取消所有消费者任务
        for task in self._consumer_tasks:
            task.cancel()
        
        # 等待任务完成
        if self._consumer_tasks:
            await asyncio.gather(*self._consumer_tasks, return_exceptions=True)
        
        self._consumer_tasks.clear()
        logger.info("Stopped consuming events")
    
    async def _consume_stream(self, event_type: EventType):
        """消费指定类型的事件流"""
        stream_name = f"{self.stream_prefix}:{event_type.value}"
        
        # 创建消费者组
        try:
            await self.redis_client.xgroup_create(
                stream_name, self.consumer_group, id="0", mkstream=True
            )
        except redis.ResponseError as e:
            if "BUSYGROUP" not in str(e):
                raise
        
        while self.running:
            try:
                # 读取消息
                messages = await self.redis_client.xreadgroup(
                    self.consumer_group,
                    self.consumer_name,
                    {stream_name: ">"},
                    count=10,
                    block=1000
                )
                
                for stream, msgs in messages:
                    for msg_id, fields in msgs:
                        await self._process_message(stream.decode(), msg_id.decode(), fields)
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error consuming stream {stream_name}: {e}")
                await asyncio.sleep(1)
    
    async def _process_message(self, stream_name: str, msg_id: str, fields: Dict[bytes, bytes]):
        """处理单个消息"""
        try:
            # 解析事件数据
            event_data_str = fields.get(b'event_data')
            if not event_data_str:
                logger.error(f"No event_data field in message {msg_id}")
                await self._ack_message(stream_name, msg_id)
                return

            event_data = json.loads(event_data_str.decode())
            event = Event.from_dict(event_data)
            
            # 获取处理器
            handlers = self.handlers.get(event.metadata.event_type, [])
            if not handlers:
                logger.warning(f"No handlers for event type: {event.metadata.event_type}")
                await self._ack_message(stream_name, msg_id)
                return
            
            # 执行处理器
            success = True
            for handler in handlers:
                try:
                    result = await handler.handle(event)
                    if not result:
                        success = False
                        break
                except Exception as e:
                    logger.error(f"Handler error: {e}")
                    should_retry = await handler.on_error(event, e)
                    if should_retry and event.metadata.retry_count < event.metadata.max_retries:
                        await self._retry_message(event)
                    success = False
                    break
            
            if success:
                await self._ack_message(stream_name, msg_id)
            
        except Exception as e:
            logger.error(f"Error processing message {msg_id}: {e}")
    
    async def _ack_message(self, stream_name: str, msg_id: str):
        """确认消息处理完成"""
        await self.redis_client.xack(stream_name, self.consumer_group, msg_id)
    
    async def _retry_message(self, event: Event):
        """重试消息"""
        event.metadata.retry_count += 1
        event.metadata.delay_seconds = min(2 ** event.metadata.retry_count, 300)  # 指数退避，最大5分钟
        
        # 延迟后重新发布
        await asyncio.sleep(event.metadata.delay_seconds)
        await self.publish(event)
    
    @asynccontextmanager
    async def lifespan(self):
        """生命周期管理"""
        await self.connect()
        try:
            yield self
        finally:
            await self.stop_consuming()
            await self.disconnect()


# 全局事件总线实例
_event_bus: Optional[EventBus] = None


def get_event_bus() -> EventBus:
    """获取全局事件总线实例"""
    global _event_bus
    if _event_bus is None:
        _event_bus = EventBus()
    return _event_bus


async def publish_event(
    event_type: EventType,
    payload: Dict[str, Any],
    source_service: str,
    user_id: Optional[str] = None,
    correlation_id: Optional[str] = None,
    priority: EventPriority = EventPriority.NORMAL
) -> str:
    """便捷的事件发布函数"""
    event = Event.create(
        event_type=event_type,
        payload=payload,
        source_service=source_service,
        user_id=user_id,
        correlation_id=correlation_id,
        priority=priority
    )
    
    event_bus = get_event_bus()
    return await event_bus.publish(event)
