"""
契约测试框架 - API契约验证和测试

提供：
- OpenAPI规范验证
- 契约测试生成
- API兼容性检查
- 自动化测试执行
- 测试报告生成
"""

import json
import logging
import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union
from pathlib import Path
from dataclasses import dataclass, asdict

import httpx
import yaml
from pydantic import BaseModel, ValidationError

logger = logging.getLogger(__name__)


@dataclass
class TestCase:
    """测试用例"""
    name: str
    method: str
    path: str
    description: str
    request_data: Optional[Dict[str, Any]] = None
    request_params: Optional[Dict[str, Any]] = None
    request_headers: Optional[Dict[str, str]] = None
    expected_status: int = 200
    expected_schema: Optional[Dict[str, Any]] = None
    expected_data: Optional[Dict[str, Any]] = None
    tags: List[str] = None


@dataclass
class TestResult:
    """测试结果"""
    test_case: TestCase
    success: bool
    status_code: int
    response_data: Dict[str, Any]
    response_time_ms: float
    error_message: Optional[str] = None
    validation_errors: List[str] = None


@dataclass
class TestReport:
    """测试报告"""
    service_name: str
    api_version: str
    test_time: datetime
    total_tests: int
    passed_tests: int
    failed_tests: int
    success_rate: float
    average_response_time: float
    test_results: List[TestResult]


class OpenAPIValidator:
    """OpenAPI规范验证器"""
    
    def __init__(self, openapi_spec: Dict[str, Any]):
        self.spec = openapi_spec
        self.paths = openapi_spec.get("paths", {})
        self.components = openapi_spec.get("components", {})
        self.schemas = self.components.get("schemas", {})
    
    def validate_response(
        self,
        method: str,
        path: str,
        status_code: int,
        response_data: Dict[str, Any]
    ) -> List[str]:
        """验证响应是否符合OpenAPI规范"""
        errors = []
        
        # 查找路径定义
        path_spec = self._find_path_spec(path)
        if not path_spec:
            errors.append(f"Path not found in OpenAPI spec: {path}")
            return errors
        
        # 查找方法定义
        method_spec = path_spec.get(method.lower())
        if not method_spec:
            errors.append(f"Method {method} not found for path {path}")
            return errors
        
        # 查找响应定义
        responses = method_spec.get("responses", {})
        response_spec = responses.get(str(status_code)) or responses.get("default")
        if not response_spec:
            errors.append(f"Response {status_code} not defined for {method} {path}")
            return errors
        
        # 验证响应内容
        content = response_spec.get("content", {})
        if content:
            json_content = content.get("application/json")
            if json_content:
                schema = json_content.get("schema")
                if schema:
                    schema_errors = self._validate_schema(response_data, schema)
                    errors.extend(schema_errors)
        
        return errors
    
    def _find_path_spec(self, path: str) -> Optional[Dict[str, Any]]:
        """查找路径规范"""
        # 直接匹配
        if path in self.paths:
            return self.paths[path]
        
        # 参数化路径匹配
        for spec_path, spec in self.paths.items():
            if self._match_path_pattern(path, spec_path):
                return spec
        
        return None
    
    def _match_path_pattern(self, actual_path: str, spec_path: str) -> bool:
        """匹配路径模式"""
        actual_parts = actual_path.strip("/").split("/")
        spec_parts = spec_path.strip("/").split("/")
        
        if len(actual_parts) != len(spec_parts):
            return False
        
        for actual, spec in zip(actual_parts, spec_parts):
            if spec.startswith("{") and spec.endswith("}"):
                continue  # 参数匹配
            if actual != spec:
                return False
        
        return True
    
    def _validate_schema(self, data: Any, schema: Dict[str, Any]) -> List[str]:
        """验证数据是否符合schema"""
        errors = []
        
        # 解析schema引用
        if "$ref" in schema:
            ref_path = schema["$ref"]
            if ref_path.startswith("#/components/schemas/"):
                schema_name = ref_path.split("/")[-1]
                schema = self.schemas.get(schema_name, {})
        
        # 验证类型
        schema_type = schema.get("type")
        if schema_type:
            if not self._validate_type(data, schema_type):
                errors.append(f"Type mismatch: expected {schema_type}, got {type(data).__name__}")
        
        # 验证对象属性
        if schema_type == "object" and isinstance(data, dict):
            properties = schema.get("properties", {})
            required = schema.get("required", [])
            
            # 检查必需属性
            for prop in required:
                if prop not in data:
                    errors.append(f"Missing required property: {prop}")
            
            # 验证属性
            for prop, value in data.items():
                if prop in properties:
                    prop_errors = self._validate_schema(value, properties[prop])
                    errors.extend([f"{prop}.{err}" for err in prop_errors])
        
        # 验证数组元素
        if schema_type == "array" and isinstance(data, list):
            items_schema = schema.get("items")
            if items_schema:
                for i, item in enumerate(data):
                    item_errors = self._validate_schema(item, items_schema)
                    errors.extend([f"[{i}].{err}" for err in item_errors])
        
        return errors
    
    def _validate_type(self, data: Any, expected_type: str) -> bool:
        """验证数据类型"""
        type_mapping = {
            "string": str,
            "integer": int,
            "number": (int, float),
            "boolean": bool,
            "array": list,
            "object": dict,
            "null": type(None)
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type:
            return isinstance(data, expected_python_type)
        
        return True


class ContractTester:
    """契约测试器"""
    
    def __init__(self, base_url: str, openapi_spec: Dict[str, Any]):
        self.base_url = base_url.rstrip("/")
        self.openapi_spec = openapi_spec
        self.validator = OpenAPIValidator(openapi_spec)
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def run_test_case(self, test_case: TestCase) -> TestResult:
        """运行单个测试用例"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # 构建请求URL
            url = f"{self.base_url}{test_case.path}"
            
            # 准备请求参数
            request_kwargs = {
                "method": test_case.method,
                "url": url,
                "params": test_case.request_params,
                "headers": test_case.request_headers or {}
            }
            
            if test_case.request_data:
                request_kwargs["json"] = test_case.request_data
            
            # 执行请求
            response = await self.client.request(**request_kwargs)
            
            # 计算响应时间
            response_time_ms = (asyncio.get_event_loop().time() - start_time) * 1000
            
            # 解析响应数据
            try:
                response_data = response.json()
            except:
                response_data = {"text": response.text}
            
            # 验证状态码
            status_match = response.status_code == test_case.expected_status
            
            # 验证响应格式
            validation_errors = self.validator.validate_response(
                test_case.method,
                test_case.path,
                response.status_code,
                response_data
            )
            
            # 验证期望数据
            data_match = True
            if test_case.expected_data:
                data_match = self._compare_data(response_data, test_case.expected_data)
                if not data_match:
                    validation_errors.append("Response data does not match expected data")
            
            # 判断测试是否成功
            success = status_match and not validation_errors and data_match
            
            return TestResult(
                test_case=test_case,
                success=success,
                status_code=response.status_code,
                response_data=response_data,
                response_time_ms=response_time_ms,
                validation_errors=validation_errors if validation_errors else None
            )
            
        except Exception as e:
            response_time_ms = (asyncio.get_event_loop().time() - start_time) * 1000
            
            return TestResult(
                test_case=test_case,
                success=False,
                status_code=0,
                response_data={},
                response_time_ms=response_time_ms,
                error_message=str(e)
            )
    
    async def run_test_suite(self, test_cases: List[TestCase]) -> TestReport:
        """运行测试套件"""
        logger.info(f"Running {len(test_cases)} test cases...")
        
        # 并发执行测试
        tasks = [self.run_test_case(test_case) for test_case in test_cases]
        test_results = await asyncio.gather(*tasks)
        
        # 统计结果
        passed_tests = sum(1 for result in test_results if result.success)
        failed_tests = len(test_results) - passed_tests
        success_rate = (passed_tests / len(test_results)) * 100 if test_results else 0
        average_response_time = sum(result.response_time_ms for result in test_results) / len(test_results) if test_results else 0
        
        # 生成报告
        report = TestReport(
            service_name=self.openapi_spec.get("info", {}).get("title", "Unknown"),
            api_version=self.openapi_spec.get("info", {}).get("version", "Unknown"),
            test_time=datetime.now(),
            total_tests=len(test_results),
            passed_tests=passed_tests,
            failed_tests=failed_tests,
            success_rate=success_rate,
            average_response_time=average_response_time,
            test_results=test_results
        )
        
        logger.info(f"Test completed: {passed_tests}/{len(test_results)} passed ({success_rate:.1f}%)")
        return report
    
    def _compare_data(self, actual: Any, expected: Any) -> bool:
        """比较数据是否匹配"""
        if isinstance(expected, dict) and isinstance(actual, dict):
            for key, value in expected.items():
                if key not in actual:
                    return False
                if not self._compare_data(actual[key], value):
                    return False
            return True
        elif isinstance(expected, list) and isinstance(actual, list):
            if len(expected) != len(actual):
                return False
            for exp_item, act_item in zip(expected, actual):
                if not self._compare_data(act_item, exp_item):
                    return False
            return True
        else:
            return actual == expected
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


def generate_test_cases_from_openapi(openapi_spec: Dict[str, Any]) -> List[TestCase]:
    """从OpenAPI规范生成测试用例"""
    test_cases = []
    
    paths = openapi_spec.get("paths", {})
    
    for path, path_spec in paths.items():
        for method, method_spec in path_spec.items():
            if method.upper() not in ["GET", "POST", "PUT", "DELETE", "PATCH"]:
                continue
            
            # 基本测试用例
            test_case = TestCase(
                name=f"{method.upper()} {path}",
                method=method.upper(),
                path=path,
                description=method_spec.get("summary", f"Test {method.upper()} {path}"),
                tags=method_spec.get("tags", [])
            )
            
            # 添加请求数据示例
            request_body = method_spec.get("requestBody")
            if request_body:
                content = request_body.get("content", {})
                json_content = content.get("application/json")
                if json_content:
                    schema = json_content.get("schema")
                    if schema:
                        test_case.request_data = generate_example_data(schema, openapi_spec)
            
            # 设置期望状态码
            responses = method_spec.get("responses", {})
            if "200" in responses:
                test_case.expected_status = 200
            elif "201" in responses:
                test_case.expected_status = 201
            elif responses:
                # 使用第一个成功状态码
                for status in responses.keys():
                    if status.startswith("2"):
                        test_case.expected_status = int(status)
                        break
            
            test_cases.append(test_case)
    
    return test_cases


def generate_example_data(schema: Dict[str, Any], openapi_spec: Dict[str, Any]) -> Any:
    """从schema生成示例数据"""
    # 处理引用
    if "$ref" in schema:
        ref_path = schema["$ref"]
        if ref_path.startswith("#/components/schemas/"):
            schema_name = ref_path.split("/")[-1]
            schemas = openapi_spec.get("components", {}).get("schemas", {})
            schema = schemas.get(schema_name, {})
    
    # 使用示例值
    if "example" in schema:
        return schema["example"]
    
    schema_type = schema.get("type", "object")
    
    if schema_type == "object":
        result = {}
        properties = schema.get("properties", {})
        for prop, prop_schema in properties.items():
            result[prop] = generate_example_data(prop_schema, openapi_spec)
        return result
    
    elif schema_type == "array":
        items_schema = schema.get("items", {})
        return [generate_example_data(items_schema, openapi_spec)]
    
    elif schema_type == "string":
        return schema.get("default", "test_string")
    
    elif schema_type == "integer":
        return schema.get("default", 123)
    
    elif schema_type == "number":
        return schema.get("default", 123.45)
    
    elif schema_type == "boolean":
        return schema.get("default", True)
    
    else:
        return None


async def run_contract_tests(
    service_url: str,
    openapi_spec_path: str,
    output_path: Optional[str] = None
) -> TestReport:
    """运行契约测试"""
    
    # 加载OpenAPI规范
    spec_file = Path(openapi_spec_path)
    if spec_file.suffix.lower() in ['.yaml', '.yml']:
        with open(spec_file, 'r', encoding='utf-8') as f:
            openapi_spec = yaml.safe_load(f)
    else:
        with open(spec_file, 'r', encoding='utf-8') as f:
            openapi_spec = json.load(f)
    
    # 生成测试用例
    test_cases = generate_test_cases_from_openapi(openapi_spec)
    
    # 运行测试
    tester = ContractTester(service_url, openapi_spec)
    try:
        report = await tester.run_test_suite(test_cases)
        
        # 保存报告
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(report), f, indent=2, default=str)
        
        return report
        
    finally:
        await tester.close()
