"""
服务治理工具 - 服务发现、健康检查和负载均衡

提供：
- 服务注册和发现
- 健康检查和监控
- 负载均衡策略
- 服务依赖管理
- 故障转移和恢复
"""

import asyncio
import json
import logging
import random
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, asdict
from collections import defaultdict

import redis.asyncio as redis

logger = logging.getLogger(__name__)


class ServiceStatus(str, Enum):
    """服务状态"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"
    MAINTENANCE = "maintenance"


class LoadBalanceStrategy(str, Enum):
    """负载均衡策略"""
    ROUND_ROBIN = "round_robin"
    RANDOM = "random"
    LEAST_CONNECTIONS = "least_connections"
    WEIGHTED_RANDOM = "weighted_random"


@dataclass
class ServiceInstance:
    """服务实例"""
    service_name: str
    instance_id: str
    host: str
    port: int
    version: str
    status: ServiceStatus = ServiceStatus.UNKNOWN
    weight: int = 1
    metadata: Dict[str, Any] = None
    last_heartbeat: Optional[float] = None
    health_check_url: Optional[str] = None
    tags: List[str] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.tags is None:
            self.tags = []
        if self.health_check_url is None:
            self.health_check_url = f"http://{self.host}:{self.port}/api/v1/health"
    
    @property
    def base_url(self) -> str:
        """获取基础URL"""
        return f"http://{self.host}:{self.port}"
    
    def is_healthy(self, heartbeat_timeout: float = 30.0) -> bool:
        """检查实例是否健康"""
        if self.status != ServiceStatus.HEALTHY:
            return False
        
        if self.last_heartbeat is None:
            return False
        
        return time.time() - self.last_heartbeat < heartbeat_timeout


@dataclass
class ServiceDependency:
    """服务依赖"""
    service_name: str
    dependent_service: str
    dependency_type: str = "required"  # required, optional
    timeout_seconds: float = 30.0
    retry_count: int = 3


class ServiceRegistry:
    """服务注册表"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis_client: Optional[redis.Redis] = None
        self.instances: Dict[str, Dict[str, ServiceInstance]] = defaultdict(dict)
        self.dependencies: Dict[str, List[ServiceDependency]] = defaultdict(list)
        self.load_balancers: Dict[str, 'LoadBalancer'] = {}
        
        # 健康检查配置
        self.health_check_interval = 10.0
        self.heartbeat_timeout = 30.0
        self.health_check_task: Optional[asyncio.Task] = None
    
    async def connect(self):
        """连接到Redis"""
        self.redis_client = redis.from_url(self.redis_url)
        await self.redis_client.ping()
        logger.info(f"ServiceRegistry connected to Redis: {self.redis_url}")
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.health_check_task:
            self.health_check_task.cancel()
        
        if self.redis_client:
            await self.redis_client.close()
            logger.info("ServiceRegistry disconnected from Redis")
    
    async def register_service(self, instance: ServiceInstance) -> bool:
        """注册服务实例"""
        try:
            # 更新本地缓存
            self.instances[instance.service_name][instance.instance_id] = instance
            
            # 存储到Redis
            if self.redis_client:
                key = f"service:{instance.service_name}:{instance.instance_id}"
                data = asdict(instance)
                data["registered_at"] = time.time()
                
                await self.redis_client.hset(key, mapping={
                    "data": json.dumps(data, default=str)
                })
                await self.redis_client.expire(key, int(self.heartbeat_timeout * 2))
            
            # 创建负载均衡器
            if instance.service_name not in self.load_balancers:
                self.load_balancers[instance.service_name] = LoadBalancer(
                    service_name=instance.service_name,
                    strategy=LoadBalanceStrategy.ROUND_ROBIN
                )
            
            logger.info(f"Registered service instance: {instance.service_name}:{instance.instance_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register service instance: {e}")
            return False
    
    async def deregister_service(self, service_name: str, instance_id: str) -> bool:
        """注销服务实例"""
        try:
            # 从本地缓存移除
            if service_name in self.instances:
                self.instances[service_name].pop(instance_id, None)
            
            # 从Redis移除
            if self.redis_client:
                key = f"service:{service_name}:{instance_id}"
                await self.redis_client.delete(key)
            
            logger.info(f"Deregistered service instance: {service_name}:{instance_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to deregister service instance: {e}")
            return False
    
    async def heartbeat(self, service_name: str, instance_id: str) -> bool:
        """发送心跳"""
        try:
            # 更新本地缓存
            if service_name in self.instances and instance_id in self.instances[service_name]:
                instance = self.instances[service_name][instance_id]
                instance.last_heartbeat = time.time()
                instance.status = ServiceStatus.HEALTHY
            
            # 更新Redis
            if self.redis_client:
                key = f"service:{service_name}:{instance_id}"
                await self.redis_client.hset(key, "last_heartbeat", time.time())
                await self.redis_client.expire(key, int(self.heartbeat_timeout * 2))
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send heartbeat: {e}")
            return False
    
    async def discover_services(self, service_name: str) -> List[ServiceInstance]:
        """发现服务实例"""
        try:
            # 从Redis加载最新数据
            if self.redis_client:
                await self._load_from_redis(service_name)
            
            # 返回健康的实例
            instances = self.instances.get(service_name, {})
            healthy_instances = [
                instance for instance in instances.values()
                if instance.is_healthy(self.heartbeat_timeout)
            ]
            
            return healthy_instances
            
        except Exception as e:
            logger.error(f"Failed to discover services: {e}")
            return []
    
    async def get_service_instance(
        self,
        service_name: str,
        strategy: LoadBalanceStrategy = LoadBalanceStrategy.ROUND_ROBIN
    ) -> Optional[ServiceInstance]:
        """获取服务实例（负载均衡）"""
        instances = await self.discover_services(service_name)
        
        if not instances:
            return None
        
        load_balancer = self.load_balancers.get(service_name)
        if not load_balancer:
            load_balancer = LoadBalancer(service_name, strategy)
            self.load_balancers[service_name] = load_balancer
        
        return load_balancer.select_instance(instances)
    
    def add_dependency(self, dependency: ServiceDependency):
        """添加服务依赖"""
        self.dependencies[dependency.service_name].append(dependency)
        logger.info(f"Added dependency: {dependency.service_name} -> {dependency.dependent_service}")
    
    async def check_dependencies(self, service_name: str) -> Dict[str, bool]:
        """检查服务依赖"""
        results = {}
        dependencies = self.dependencies.get(service_name, [])
        
        for dep in dependencies:
            instances = await self.discover_services(dep.dependent_service)
            results[dep.dependent_service] = len(instances) > 0
        
        return results
    
    async def start_health_check(self):
        """启动健康检查"""
        if self.health_check_task:
            return
        
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        logger.info("Started health check loop")
    
    async def stop_health_check(self):
        """停止健康检查"""
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
            self.health_check_task = None
            logger.info("Stopped health check loop")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        import httpx
        
        async with httpx.AsyncClient(timeout=5.0) as client:
            while True:
                try:
                    # 检查所有服务实例
                    for service_name, instances in self.instances.items():
                        for instance_id, instance in instances.items():
                            try:
                                # 发送健康检查请求
                                response = await client.get(instance.health_check_url)
                                
                                if response.status_code == 200:
                                    instance.status = ServiceStatus.HEALTHY
                                    instance.last_heartbeat = time.time()
                                else:
                                    instance.status = ServiceStatus.UNHEALTHY
                                    
                            except Exception as e:
                                logger.debug(f"Health check failed for {service_name}:{instance_id}: {e}")
                                instance.status = ServiceStatus.UNHEALTHY
                    
                    await asyncio.sleep(self.health_check_interval)
                    
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.error(f"Health check loop error: {e}")
                    await asyncio.sleep(1)
    
    async def _load_from_redis(self, service_name: str):
        """从Redis加载服务数据"""
        if not self.redis_client:
            return
        
        try:
            pattern = f"service:{service_name}:*"
            keys = await self.redis_client.keys(pattern)
            
            for key in keys:
                data = await self.redis_client.hget(key, "data")
                if data:
                    instance_data = json.loads(data.decode())
                    # 移除不属于ServiceInstance的字段
                    instance_data.pop('registered_at', None)
                    instance = ServiceInstance(**instance_data)
                    self.instances[service_name][instance.instance_id] = instance
                    
        except Exception as e:
            logger.error(f"Failed to load from Redis: {e}")


class LoadBalancer:
    """负载均衡器"""
    
    def __init__(self, service_name: str, strategy: LoadBalanceStrategy = LoadBalanceStrategy.ROUND_ROBIN):
        self.service_name = service_name
        self.strategy = strategy
        self.round_robin_index = 0
        self.connection_counts: Dict[str, int] = defaultdict(int)
    
    def select_instance(self, instances: List[ServiceInstance]) -> Optional[ServiceInstance]:
        """选择服务实例"""
        if not instances:
            return None
        
        if self.strategy == LoadBalanceStrategy.ROUND_ROBIN:
            return self._round_robin_select(instances)
        elif self.strategy == LoadBalanceStrategy.RANDOM:
            return self._random_select(instances)
        elif self.strategy == LoadBalanceStrategy.LEAST_CONNECTIONS:
            return self._least_connections_select(instances)
        elif self.strategy == LoadBalanceStrategy.WEIGHTED_RANDOM:
            return self._weighted_random_select(instances)
        else:
            return instances[0]
    
    def _round_robin_select(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """轮询选择"""
        instance = instances[self.round_robin_index % len(instances)]
        self.round_robin_index += 1
        return instance
    
    def _random_select(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """随机选择"""
        return random.choice(instances)
    
    def _least_connections_select(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """最少连接选择"""
        min_connections = float('inf')
        selected_instance = instances[0]
        
        for instance in instances:
            connections = self.connection_counts[instance.instance_id]
            if connections < min_connections:
                min_connections = connections
                selected_instance = instance
        
        return selected_instance
    
    def _weighted_random_select(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """加权随机选择"""
        total_weight = sum(instance.weight for instance in instances)
        if total_weight == 0:
            return random.choice(instances)
        
        random_weight = random.uniform(0, total_weight)
        current_weight = 0
        
        for instance in instances:
            current_weight += instance.weight
            if current_weight >= random_weight:
                return instance
        
        return instances[-1]
    
    def record_connection(self, instance_id: str):
        """记录连接"""
        self.connection_counts[instance_id] += 1
    
    def release_connection(self, instance_id: str):
        """释放连接"""
        if self.connection_counts[instance_id] > 0:
            self.connection_counts[instance_id] -= 1


# 全局服务注册表
_service_registry: Optional[ServiceRegistry] = None


def get_service_registry() -> ServiceRegistry:
    """获取服务注册表"""
    global _service_registry
    if _service_registry is None:
        _service_registry = ServiceRegistry()
    return _service_registry


async def register_current_service(
    service_name: str,
    host: str,
    port: int,
    version: str,
    metadata: Optional[Dict[str, Any]] = None
) -> str:
    """注册当前服务"""
    import uuid
    
    instance_id = str(uuid.uuid4())
    instance = ServiceInstance(
        service_name=service_name,
        instance_id=instance_id,
        host=host,
        port=port,
        version=version,
        metadata=metadata or {},
        status=ServiceStatus.HEALTHY,
        last_heartbeat=time.time()
    )
    
    registry = get_service_registry()
    await registry.connect()
    await registry.register_service(instance)
    
    # 启动心跳任务
    async def heartbeat_task():
        while True:
            try:
                await registry.heartbeat(service_name, instance_id)
                await asyncio.sleep(10)  # 每10秒发送一次心跳
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Heartbeat failed: {e}")
                await asyncio.sleep(5)
    
    asyncio.create_task(heartbeat_task())
    
    logger.info(f"Registered current service: {service_name}:{instance_id}")
    return instance_id
