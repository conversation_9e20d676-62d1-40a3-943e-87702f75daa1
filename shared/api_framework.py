"""
API框架 - 统一的API版本化和服务间通信标准

提供：
- API版本化管理
- 统一的请求/响应格式
- 错误处理标准化
- 服务间通信协议
- 契约测试支持
"""

import json
import logging
import time
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Type
from dataclasses import dataclass, asdict

from fastapi import FastAPI, Request, Response, HTTPException, Depends
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.openapi.utils import get_openapi
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class APIVersion(str, Enum):
    """API版本枚举"""
    V1 = "v1"
    V2 = "v2"
    BETA = "beta"


class ErrorCode(str, Enum):
    """标准错误代码"""
    # 通用错误
    INTERNAL_ERROR = "INTERNAL_ERROR"
    INVALID_REQUEST = "INVALID_REQUEST"
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    NOT_FOUND = "NOT_FOUND"
    CONFLICT = "CONFLICT"
    RATE_LIMITED = "RATE_LIMITED"
    
    # 业务错误
    VALIDATION_ERROR = "VALIDATION_ERROR"
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    RESOURCE_ALREADY_EXISTS = "RESOURCE_ALREADY_EXISTS"
    OPERATION_FAILED = "OPERATION_FAILED"
    
    # 服务错误
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    DEPENDENCY_ERROR = "DEPENDENCY_ERROR"
    TIMEOUT_ERROR = "TIMEOUT_ERROR"


@dataclass
class APIMetadata:
    """API元数据"""
    request_id: str
    timestamp: float
    version: APIVersion
    service_name: str
    endpoint: str
    method: str
    user_id: Optional[str] = None
    correlation_id: Optional[str] = None
    processing_time_ms: Optional[float] = None


class BaseAPIRequest(BaseModel):
    """API请求基类"""
    request_id: Optional[str] = Field(default_factory=lambda: str(uuid.uuid4()))
    correlation_id: Optional[str] = None
    user_id: Optional[str] = None
    
    class Config:
        extra = "forbid"  # 禁止额外字段


class BaseAPIResponse(BaseModel):
    """API响应基类"""
    success: bool = True
    message: str = "Operation completed successfully"
    request_id: str
    timestamp: float = Field(default_factory=time.time)
    version: APIVersion = APIVersion.V1
    
    class Config:
        extra = "allow"


class APIError(BaseModel):
    """API错误响应"""
    success: bool = False
    error_code: ErrorCode
    message: str
    details: Optional[Dict[str, Any]] = None
    request_id: str
    timestamp: float = Field(default_factory=time.time)
    version: APIVersion = APIVersion.V1


class PaginationRequest(BaseModel):
    """分页请求参数"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页大小")
    sort_by: Optional[str] = Field(default=None, description="排序字段")
    sort_order: Optional[str] = Field(default="asc", pattern="^(asc|desc)$", description="排序方向")


class PaginationResponse(BaseModel):
    """分页响应"""
    page: int
    page_size: int
    total_count: int
    total_pages: int
    has_next: bool
    has_prev: bool


class DataResponse(BaseAPIResponse):
    """数据响应"""
    data: Any = None
    pagination: Optional[PaginationResponse] = None


class ServiceInfo(BaseModel):
    """服务信息"""
    name: str
    version: str
    description: str
    api_version: APIVersion
    health_status: str
    uptime_seconds: float
    dependencies: List[str] = Field(default_factory=list)


class APIVersionMiddleware(BaseHTTPMiddleware):
    """API版本化中间件"""
    
    def __init__(self, app: FastAPI, service_name: str, default_version: APIVersion = APIVersion.V1):
        super().__init__(app)
        self.service_name = service_name
        self.default_version = default_version
    
    async def dispatch(self, request: Request, call_next):
        # 提取API版本
        api_version = self._extract_version(request)
        
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 添加到请求状态
        request.state.api_version = api_version
        request.state.request_id = request_id
        request.state.service_name = self.service_name
        request.state.start_time = time.time()
        
        # 添加请求头
        request.headers.__dict__["_list"].append(
            (b"x-request-id", request_id.encode())
        )
        
        try:
            response = await call_next(request)
            
            # 计算处理时间
            processing_time = (time.time() - request.state.start_time) * 1000
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-API-Version"] = api_version.value
            response.headers["X-Service-Name"] = self.service_name
            response.headers["X-Processing-Time-MS"] = str(round(processing_time, 2))
            
            return response
            
        except Exception as e:
            logger.error(f"Request {request_id} failed: {e}")
            return self._create_error_response(
                error_code=ErrorCode.INTERNAL_ERROR,
                message=str(e),
                request_id=request_id,
                api_version=api_version
            )
    
    def _extract_version(self, request: Request) -> APIVersion:
        """提取API版本"""
        # 从URL路径提取版本
        path_parts = request.url.path.strip("/").split("/")
        if len(path_parts) >= 2 and path_parts[0] == "api":
            version_str = path_parts[1]
            try:
                return APIVersion(version_str)
            except ValueError:
                pass
        
        # 从请求头提取版本
        version_header = request.headers.get("X-API-Version")
        if version_header:
            try:
                return APIVersion(version_header)
            except ValueError:
                pass
        
        # 返回默认版本
        return self.default_version
    
    def _create_error_response(
        self,
        error_code: ErrorCode,
        message: str,
        request_id: str,
        api_version: APIVersion,
        status_code: int = 500
    ) -> JSONResponse:
        """创建错误响应"""
        error_response = APIError(
            error_code=error_code,
            message=message,
            request_id=request_id,
            version=api_version
        )
        
        return JSONResponse(
            status_code=status_code,
            content=error_response.dict()
        )


class APIExceptionHandler:
    """API异常处理器"""
    
    @staticmethod
    def create_error_response(
        error_code: ErrorCode,
        message: str,
        request_id: str,
        details: Optional[Dict[str, Any]] = None,
        status_code: int = 400
    ) -> JSONResponse:
        """创建标准错误响应"""
        error_response = APIError(
            error_code=error_code,
            message=message,
            details=details,
            request_id=request_id
        )
        
        return JSONResponse(
            status_code=status_code,
            content=error_response.dict()
        )
    
    @staticmethod
    def handle_validation_error(request_id: str, errors: List[Dict]) -> JSONResponse:
        """处理验证错误"""
        return APIExceptionHandler.create_error_response(
            error_code=ErrorCode.VALIDATION_ERROR,
            message="Request validation failed",
            request_id=request_id,
            details={"validation_errors": errors},
            status_code=422
        )
    
    @staticmethod
    def handle_not_found(request_id: str, resource: str) -> JSONResponse:
        """处理资源未找到错误"""
        return APIExceptionHandler.create_error_response(
            error_code=ErrorCode.RESOURCE_NOT_FOUND,
            message=f"{resource} not found",
            request_id=request_id,
            status_code=404
        )
    
    @staticmethod
    def handle_conflict(request_id: str, message: str) -> JSONResponse:
        """处理冲突错误"""
        return APIExceptionHandler.create_error_response(
            error_code=ErrorCode.CONFLICT,
            message=message,
            request_id=request_id,
            status_code=409
        )


def create_success_response(
    data: Any = None,
    message: str = "Operation completed successfully",
    request_id: str = None,
    pagination: Optional[PaginationResponse] = None
) -> DataResponse:
    """创建成功响应"""
    if request_id is None:
        request_id = str(uuid.uuid4())
    
    return DataResponse(
        data=data,
        message=message,
        request_id=request_id,
        pagination=pagination
    )


def get_request_metadata(request: Request) -> APIMetadata:
    """获取请求元数据"""
    return APIMetadata(
        request_id=getattr(request.state, "request_id", str(uuid.uuid4())),
        timestamp=time.time(),
        version=getattr(request.state, "api_version", APIVersion.V1),
        service_name=getattr(request.state, "service_name", "unknown"),
        endpoint=request.url.path,
        method=request.method,
        correlation_id=request.headers.get("X-Correlation-ID"),
        user_id=request.headers.get("X-User-ID")
    )


class ServiceRegistry:
    """服务注册表"""
    
    def __init__(self):
        self.services: Dict[str, ServiceInfo] = {}
    
    def register_service(self, service_info: ServiceInfo):
        """注册服务"""
        self.services[service_info.name] = service_info
        logger.info(f"Registered service: {service_info.name} v{service_info.version}")
    
    def get_service(self, name: str) -> Optional[ServiceInfo]:
        """获取服务信息"""
        return self.services.get(name)
    
    def list_services(self) -> List[ServiceInfo]:
        """列出所有服务"""
        return list(self.services.values())
    
    def update_service_health(self, name: str, health_status: str):
        """更新服务健康状态"""
        if name in self.services:
            self.services[name].health_status = health_status


# 全局服务注册表
_service_registry = ServiceRegistry()


def get_service_registry() -> ServiceRegistry:
    """获取服务注册表"""
    return _service_registry


def setup_api_framework(
    app: FastAPI,
    service_name: str,
    service_version: str,
    service_description: str,
    default_api_version: APIVersion = APIVersion.V1
) -> FastAPI:
    """设置API框架"""

    # 设置OpenAPI规范
    def custom_openapi():
        if app.openapi_schema:
            return app.openapi_schema

        openapi_schema = get_openapi(
            title=f"{service_name} API",
            version=service_version,
            description=service_description,
            routes=app.routes,
        )

        # 添加API版本信息
        openapi_schema["info"]["x-api-version"] = default_api_version.value
        openapi_schema["info"]["x-service-name"] = service_name

        # 添加标准错误响应
        if "components" not in openapi_schema:
            openapi_schema["components"] = {}
        if "schemas" not in openapi_schema["components"]:
            openapi_schema["components"]["schemas"] = {}

        # 添加标准响应模型
        openapi_schema["components"]["schemas"]["APIError"] = {
            "type": "object",
            "properties": {
                "success": {"type": "boolean", "default": False},
                "error_code": {"type": "string"},
                "message": {"type": "string"},
                "details": {"type": "object"},
                "request_id": {"type": "string"},
                "timestamp": {"type": "number"},
                "version": {"type": "string"}
            },
            "required": ["success", "error_code", "message", "request_id", "timestamp", "version"]
        }

        app.openapi_schema = openapi_schema
        return app.openapi_schema

    app.openapi = custom_openapi

    # 添加版本化中间件
    app.add_middleware(APIVersionMiddleware, service_name=service_name, default_version=default_api_version)

    # 注册服务
    service_info = ServiceInfo(
        name=service_name,
        version=service_version,
        description=service_description,
        api_version=default_api_version,
        health_status="healthy",
        uptime_seconds=0.0
    )
    get_service_registry().register_service(service_info)
    
    # 添加全局异常处理
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        request_id = getattr(request.state, "request_id", str(uuid.uuid4()))
        
        error_code_map = {
            400: ErrorCode.INVALID_REQUEST,
            401: ErrorCode.UNAUTHORIZED,
            403: ErrorCode.FORBIDDEN,
            404: ErrorCode.NOT_FOUND,
            409: ErrorCode.CONFLICT,
            429: ErrorCode.RATE_LIMITED,
            500: ErrorCode.INTERNAL_ERROR,
            503: ErrorCode.SERVICE_UNAVAILABLE
        }
        
        error_code = error_code_map.get(exc.status_code, ErrorCode.INTERNAL_ERROR)
        
        return APIExceptionHandler.create_error_response(
            error_code=error_code,
            message=exc.detail,
            request_id=request_id,
            status_code=exc.status_code
        )
    
    # 添加服务信息端点
    @app.get("/api/{version}/service/info", response_model=ServiceInfo)
    async def get_service_info(request: Request):
        """获取服务信息"""
        service_name = getattr(request.state, "service_name", "unknown")
        service = get_service_registry().get_service(service_name)
        
        if not service:
            raise HTTPException(status_code=404, detail="Service not found")
        
        return service
    
    logger.info(f"API framework setup completed for service: {service_name}")
    return app
