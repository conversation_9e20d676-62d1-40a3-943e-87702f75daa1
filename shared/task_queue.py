"""
任务队列管理器 - 基于Redis的异步任务处理

提供高性能的任务队列管理，支持：
- 任务优先级和调度
- 任务状态跟踪
- 失败重试和死信队列
- 任务结果存储
- 批量处理能力
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass, asdict

import redis.asyncio as redis
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待处理
    RUNNING = "running"      # 正在处理
    SUCCESS = "success"      # 处理成功
    FAILURE = "failure"      # 处理失败
    RETRY = "retry"          # 等待重试
    CANCELLED = "cancelled"  # 已取消
    TIMEOUT = "timeout"      # 处理超时


class TaskPriority(int, Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class TaskMetadata:
    """任务元数据"""
    task_id: str
    task_type: str
    created_at: float
    updated_at: float
    status: TaskStatus = TaskStatus.PENDING
    priority: TaskPriority = TaskPriority.NORMAL
    retry_count: int = 0
    max_retries: int = 3
    timeout_seconds: int = 300
    correlation_id: Optional[str] = None
    user_id: Optional[str] = None
    worker_id: Optional[str] = None
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    error_message: Optional[str] = None


class Task(BaseModel):
    """任务基类"""
    metadata: TaskMetadata
    payload: Dict[str, Any] = Field(default_factory=dict)
    result: Optional[Dict[str, Any]] = None
    
    @classmethod
    def create(
        cls,
        task_type: str,
        payload: Dict[str, Any],
        priority: TaskPriority = TaskPriority.NORMAL,
        timeout_seconds: int = 300,
        max_retries: int = 3,
        user_id: Optional[str] = None,
        correlation_id: Optional[str] = None
    ) -> "Task":
        """创建任务实例"""
        now = time.time()
        return cls(
            metadata=TaskMetadata(
                task_id=str(uuid.uuid4()),
                task_type=task_type,
                created_at=now,
                updated_at=now,
                priority=priority,
                timeout_seconds=timeout_seconds,
                max_retries=max_retries,
                user_id=user_id,
                correlation_id=correlation_id or str(uuid.uuid4())
            ),
            payload=payload
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "metadata": asdict(self.metadata),
            "payload": self.payload,
            "result": self.result
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Task":
        """从字典创建任务实例"""
        metadata_data = data["metadata"]
        metadata = TaskMetadata(
            task_id=metadata_data["task_id"],
            task_type=metadata_data["task_type"],
            created_at=metadata_data["created_at"],
            updated_at=metadata_data["updated_at"],
            status=TaskStatus(metadata_data.get("status", TaskStatus.PENDING)),
            priority=TaskPriority(metadata_data.get("priority", TaskPriority.NORMAL)),
            retry_count=metadata_data.get("retry_count", 0),
            max_retries=metadata_data.get("max_retries", 3),
            timeout_seconds=metadata_data.get("timeout_seconds", 300),
            correlation_id=metadata_data.get("correlation_id"),
            user_id=metadata_data.get("user_id"),
            worker_id=metadata_data.get("worker_id"),
            started_at=metadata_data.get("started_at"),
            completed_at=metadata_data.get("completed_at"),
            error_message=metadata_data.get("error_message")
        )
        return cls(
            metadata=metadata,
            payload=data.get("payload", {}),
            result=data.get("result")
        )


class TaskProcessor:
    """任务处理器基类"""
    
    def __init__(self, task_types: List[str]):
        self.task_types = task_types
    
    async def process(self, task: Task) -> Dict[str, Any]:
        """
        处理任务
        
        Args:
            task: 要处理的任务
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        raise NotImplementedError
    
    async def on_success(self, task: Task, result: Dict[str, Any]):
        """任务成功处理回调"""
        logger.info(f"Task {task.metadata.task_id} completed successfully")
    
    async def on_failure(self, task: Task, error: Exception):
        """任务处理失败回调"""
        logger.error(f"Task {task.metadata.task_id} failed: {error}")
    
    async def on_retry(self, task: Task, error: Exception):
        """任务重试回调"""
        logger.warning(f"Task {task.metadata.task_id} will be retried: {error}")


class TaskQueue:
    """任务队列管理器"""
    
    def __init__(
        self,
        redis_url: str = "redis://localhost:6379",
        queue_prefix: str = "tasks",
        worker_id: Optional[str] = None
    ):
        self.redis_url = redis_url
        self.queue_prefix = queue_prefix
        self.worker_id = worker_id or f"worker-{uuid.uuid4().hex[:8]}"
        
        self.redis_client: Optional[redis.Redis] = None
        self.processors: Dict[str, TaskProcessor] = {}
        self.running = False
        self._worker_tasks: List[asyncio.Task] = []
    
    async def connect(self):
        """连接到Redis"""
        self.redis_client = redis.from_url(self.redis_url)
        await self.redis_client.ping()
        logger.info(f"TaskQueue connected to Redis: {self.redis_url}")
    
    async def disconnect(self):
        """断开Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            logger.info("TaskQueue disconnected from Redis")
    
    def register_processor(self, processor: TaskProcessor):
        """注册任务处理器"""
        for task_type in processor.task_types:
            self.processors[task_type] = processor
        logger.info(f"Registered processor for task types: {processor.task_types}")
    
    async def enqueue(self, task: Task) -> str:
        """将任务加入队列"""
        if not self.redis_client:
            raise RuntimeError("TaskQueue not connected")
        
        # 根据优先级选择队列
        queue_name = self._get_queue_name(task.metadata.priority)
        
        # 存储任务数据
        task_key = f"{self.queue_prefix}:task:{task.metadata.task_id}"
        await self.redis_client.hset(task_key, mapping={
            "data": json.dumps(task.to_dict()),
            "status": task.metadata.status.value,
            "created_at": task.metadata.created_at,
            "priority": task.metadata.priority.value
        })
        
        # 设置过期时间（7天）
        await self.redis_client.expire(task_key, 7 * 24 * 3600)
        
        # 加入队列
        await self.redis_client.lpush(queue_name, task.metadata.task_id)
        
        logger.info(f"Enqueued task {task.metadata.task_id} to {queue_name}")
        return task.metadata.task_id
    
    async def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        if not self.redis_client:
            raise RuntimeError("TaskQueue not connected")
        
        task_key = f"{self.queue_prefix}:task:{task_id}"
        status = await self.redis_client.hget(task_key, "status")
        return TaskStatus(status.decode()) if status else None
    
    async def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务详情"""
        if not self.redis_client:
            raise RuntimeError("TaskQueue not connected")
        
        task_key = f"{self.queue_prefix}:task:{task_id}"
        task_data = await self.redis_client.hget(task_key, "data")
        
        if task_data:
            return Task.from_dict(json.loads(task_data.decode()))
        return None
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if not self.redis_client:
            raise RuntimeError("TaskQueue not connected")
        
        task = await self.get_task(task_id)
        if not task:
            return False
        
        if task.metadata.status in [TaskStatus.SUCCESS, TaskStatus.FAILURE, TaskStatus.CANCELLED]:
            return False
        
        # 更新任务状态
        task.metadata.status = TaskStatus.CANCELLED
        task.metadata.updated_at = time.time()
        
        await self._update_task(task)
        logger.info(f"Cancelled task {task_id}")
        return True
    
    async def start_workers(self, num_workers: int = 1):
        """启动工作进程"""
        if not self.redis_client:
            raise RuntimeError("TaskQueue not connected")
        
        self.running = True
        
        # 创建工作进程
        for i in range(num_workers):
            worker_task = asyncio.create_task(self._worker_loop(f"{self.worker_id}-{i}"))
            self._worker_tasks.append(worker_task)
        
        logger.info(f"Started {num_workers} workers")
    
    async def stop_workers(self):
        """停止工作进程"""
        self.running = False
        
        # 取消所有工作任务
        for task in self._worker_tasks:
            task.cancel()
        
        # 等待任务完成
        if self._worker_tasks:
            await asyncio.gather(*self._worker_tasks, return_exceptions=True)
        
        self._worker_tasks.clear()
        logger.info("Stopped all workers")
    
    def _get_queue_name(self, priority: TaskPriority) -> str:
        """根据优先级获取队列名称"""
        priority_suffix = {
            TaskPriority.CRITICAL: "critical",
            TaskPriority.HIGH: "high",
            TaskPriority.NORMAL: "normal",
            TaskPriority.LOW: "low"
        }
        return f"{self.queue_prefix}:queue:{priority_suffix[priority]}"
    
    async def _worker_loop(self, worker_name: str):
        """工作进程主循环"""
        logger.info(f"Worker {worker_name} started")
        
        # 按优先级顺序检查队列
        queue_names = [
            self._get_queue_name(TaskPriority.CRITICAL),
            self._get_queue_name(TaskPriority.HIGH),
            self._get_queue_name(TaskPriority.NORMAL),
            self._get_queue_name(TaskPriority.LOW)
        ]
        
        while self.running:
            try:
                # 从高优先级队列开始检查
                task_id = None
                for queue_name in queue_names:
                    task_id = await self.redis_client.brpop(queue_name, timeout=1)
                    if task_id:
                        task_id = task_id[1].decode()
                        break
                
                if task_id:
                    await self._process_task(task_id, worker_name)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Worker {worker_name} error: {e}")
                await asyncio.sleep(1)
        
        logger.info(f"Worker {worker_name} stopped")
    
    async def _process_task(self, task_id: str, worker_name: str):
        """处理单个任务"""
        try:
            # 获取任务
            task = await self.get_task(task_id)
            if not task:
                logger.warning(f"Task {task_id} not found")
                return
            
            # 检查任务状态
            if task.metadata.status != TaskStatus.PENDING:
                logger.warning(f"Task {task_id} status is {task.metadata.status}, skipping")
                return
            
            # 获取处理器
            processor = self.processors.get(task.metadata.task_type)
            if not processor:
                logger.error(f"No processor for task type: {task.metadata.task_type}")
                task.metadata.status = TaskStatus.FAILURE
                task.metadata.error_message = f"No processor for task type: {task.metadata.task_type}"
                await self._update_task(task)
                return
            
            # 更新任务状态为运行中
            task.metadata.status = TaskStatus.RUNNING
            task.metadata.worker_id = worker_name
            task.metadata.started_at = time.time()
            task.metadata.updated_at = time.time()
            await self._update_task(task)
            
            # 处理任务
            try:
                result = await asyncio.wait_for(
                    processor.process(task),
                    timeout=task.metadata.timeout_seconds
                )
                
                # 处理成功
                task.metadata.status = TaskStatus.SUCCESS
                task.metadata.completed_at = time.time()
                task.metadata.updated_at = time.time()
                task.result = result
                
                await processor.on_success(task, result)
                
            except asyncio.TimeoutError:
                # 处理超时
                task.metadata.status = TaskStatus.TIMEOUT
                task.metadata.error_message = "Task processing timeout"
                await processor.on_failure(task, TimeoutError("Task processing timeout"))
                
            except Exception as e:
                # 处理失败
                task.metadata.error_message = str(e)
                
                # 检查是否需要重试
                if task.metadata.retry_count < task.metadata.max_retries:
                    task.metadata.retry_count += 1
                    task.metadata.status = TaskStatus.RETRY
                    await processor.on_retry(task, e)
                    
                    # 延迟重试
                    delay = min(2 ** task.metadata.retry_count, 300)  # 指数退避，最大5分钟
                    await asyncio.sleep(delay)
                    
                    # 重新加入队列
                    task.metadata.status = TaskStatus.PENDING
                    task.metadata.worker_id = None
                    task.metadata.started_at = None
                    await self.enqueue(task)
                else:
                    task.metadata.status = TaskStatus.FAILURE
                    await processor.on_failure(task, e)
            
            # 更新任务状态
            task.metadata.updated_at = time.time()
            await self._update_task(task)
            
        except Exception as e:
            logger.error(f"Error processing task {task_id}: {e}")
    
    async def _update_task(self, task: Task):
        """更新任务数据"""
        task_key = f"{self.queue_prefix}:task:{task.metadata.task_id}"
        await self.redis_client.hset(task_key, mapping={
            "data": json.dumps(task.to_dict()),
            "status": task.metadata.status.value,
            "updated_at": task.metadata.updated_at
        })


# 全局任务队列实例
_task_queue: Optional[TaskQueue] = None


def get_task_queue() -> TaskQueue:
    """获取全局任务队列实例"""
    global _task_queue
    if _task_queue is None:
        _task_queue = TaskQueue()
    return _task_queue
