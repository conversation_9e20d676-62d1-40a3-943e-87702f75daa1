"""
用户服务主入口

基于FastAPI的用户服务，集成统一健康检查
"""

import sys
import os
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))
sys.path.append(os.path.join(os.path.dirname(__file__), './'))

from services.health_service import get_health_status, get_ready_status
from utils.config import get_settings

# 获取配置
settings = get_settings()

# 创建FastAPI应用
app = FastAPI(
    title=settings.api_title,
    version=settings.api_version,
    description=settings.api_description
)

# 添加CORS中间件
if settings.enable_cors:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )


@app.get("/")
async def root():
    """根路径"""
    return {
        "service": "user_service",
        "version": settings.api_version,
        "status": "running",
        "message": "User Service is operational"
    }


@app.get("/api/v1/health")
async def health_check():
    """标准健康检查接口"""
    return await get_health_status()


@app.get("/api/v1/ready")
async def readiness_check():
    """就绪检查接口（Kubernetes就绪探针）"""
    return await get_ready_status()


@app.get("/api/v1/users/me")
async def get_current_user():
    """获取当前用户（简化版本）"""
    return {
        "id": 1,
        "username": "test_user",
        "email": "<EMAIL>",
        "status": "active"
    }


if __name__ == "__main__":
    import uvicorn
    
    print(f"🚀 启动用户服务...")
    print(f"📍 监听地址: {settings.api_host}:{settings.api_port}")
    print(f"🏥 健康检查: http://{settings.api_host}:{settings.api_port}/api/v1/health")
    print(f"✅ 就绪检查: http://{settings.api_host}:{settings.api_port}/api/v1/ready")
    
    uvicorn.run(
        app,
        host=settings.api_host,
        port=settings.api_port,
        log_level=settings.log_level.lower()
    )
