"""
用户服务健康检查

基于统一健康检查框架的用户服务健康检查实现
"""

import sys
import os
from typing import Dict

# 添加项目根目录到路径
project_root = os.path.join(os.path.dirname(__file__), '../../')
sys.path.append(project_root)
from scripts.health.health_framework import HealthChecker, ServiceHealth, HealthStatus, DatabaseHealthChecker

# 直接导入配置模块
sys.path.append(os.path.join(os.path.dirname(__file__), '../'))
from utils.config import get_settings


class UserServiceHealthChecker(HealthChecker):
    """用户服务健康检查器"""
    
    def __init__(self):
        super().__init__("user_service", "1.0.0")
        self.settings = get_settings()
    
    async def check_dependencies(self) -> Dict[str, ServiceHealth]:
        """检查依赖服务健康状态"""
        dependencies = {}
        
        # 检查数据库连接
        if hasattr(self.settings, 'database_url') and self.settings.database_url:
            db_health = await DatabaseHealthChecker.check_postgresql(
                self.settings.database_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["database"] = db_health
        
        # 检查Redis连接（如果启用）
        if (hasattr(self.settings, 'enable_redis') and self.settings.enable_redis and 
            hasattr(self.settings, 'redis_url') and self.settings.redis_url):
            redis_health = await DatabaseHealthChecker.check_redis(
                self.settings.redis_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["redis"] = redis_health
        
        return dependencies
    
    async def check_self(self) -> ServiceHealth:
        """检查自身服务健康状态"""
        try:
            # 检查服务基本功能
            uptime = self.get_uptime()
            
            # 检查JWT配置
            jwt_configured = (hasattr(self.settings, 'jwt_secret_key') and 
                            self.settings.jwt_secret_key and 
                            self.settings.jwt_secret_key != "your-secret-key-change-in-production")
            
            # 检查用户管理功能
            user_registration_enabled = (hasattr(self.settings, 'enable_user_registration') and 
                                       self.settings.enable_user_registration)
            
            details = {
                "uptime_seconds": uptime,
                "jwt_configured": jwt_configured,
                "user_registration_enabled": user_registration_enabled,
                "max_login_attempts": getattr(self.settings, 'max_login_attempts', 5),
                "session_timeout": getattr(self.settings, 'session_timeout', 3600)
            }
            
            # 确定状态
            status = HealthStatus.HEALTHY
            message = "User service is operational"
            
            if not jwt_configured:
                status = HealthStatus.WARNING
                message = "JWT not properly configured for production"
            
            return ServiceHealth(
                name=self.service_name,
                status=status,
                response_time_ms=0.0,
                message=message,
                details=details,
                timestamp=self.get_current_timestamp()
            )
            
        except Exception as e:
            return ServiceHealth(
                name=self.service_name,
                status=HealthStatus.ERROR,
                response_time_ms=0.0,
                message=f"Self check failed: {str(e)}",
                timestamp=self.get_current_timestamp()
            )
    
    def get_uptime(self) -> float:
        """获取服务运行时间"""
        import time
        return time.time() - self.start_time
    
    def get_current_timestamp(self) -> float:
        """获取当前时间戳"""
        import time
        return time.time()


# 全局健康检查器实例
_health_checker = None

def get_health_checker() -> UserServiceHealthChecker:
    """获取健康检查器实例（单例模式）"""
    global _health_checker
    if _health_checker is None:
        _health_checker = UserServiceHealthChecker()
    return _health_checker


async def get_health_status():
    """获取健康状态（FastAPI兼容）"""
    checker = get_health_checker()
    return await checker.get_health_status()


async def get_ready_status():
    """获取就绪状态（Kubernetes就绪探针）"""
    checker = get_health_checker()
    health = await checker.get_health_status()
    
    # 就绪检查更严格，所有依赖都必须健康
    if health.status in [HealthStatus.HEALTHY, HealthStatus.WARNING]:
        return {
            "status": "ready",
            "service": "user_service",
            "timestamp": health.timestamp
        }
    else:
        return {
            "status": "not_ready",
            "service": "user_service",
            "message": health.message,
            "timestamp": health.timestamp
        }


if __name__ == "__main__":
    import asyncio
    
    async def test_health_check():
        """测试健康检查功能"""
        print("🧪 测试用户服务健康检查...")
        
        checker = get_health_checker()
        health = await checker.get_health_status()
        
        print(f"整体状态: {health.status}")
        print(f"服务名称: {health.service_name}")
        print(f"版本: {health.version}")
        print(f"运行时间: {health.uptime_seconds:.2f}秒")
        print(f"消息: {health.message}")
        
        print("\n服务检查结果:")
        for name, service in health.services.items():
            print(f"  {name}: {service.status} ({service.response_time_ms:.2f}ms)")
            if service.message:
                print(f"    消息: {service.message}")
        
        print(f"\n系统状态:")
        print(f"  CPU: {health.system.cpu_percent:.1f}%")
        print(f"  内存: {health.system.memory_percent:.1f}%")
        print(f"  磁盘: {health.system.disk_percent:.1f}%")
        print(f"  系统状态: {health.system.status}")
        
        # 测试就绪状态
        ready = await get_ready_status()
        print(f"\n就绪状态: {ready['status']}")
    
    asyncio.run(test_health_check())
